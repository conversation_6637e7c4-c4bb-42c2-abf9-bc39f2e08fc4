#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO11 检测API服务器
支持图像和视频的目标检测，提供RESTful API接口
"""

from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
from ultralytics import YOLO
import cv2
import numpy as np
import base64
import io
import os
import tempfile
import logging
from PIL import Image
import json
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 全局变量
models = {}
default_models = {
    'yolo11n': None,
    'yolo11s': None,
    'yolo11m': None
}

# COCO数据集类别名称
COCO_CLASSES = [
    'person', 'bicycle', 'car', 'motorcycle', 'airplane', 'bus', 'train', 'truck',
    'boat', 'traffic light', 'fire hydrant', 'stop sign', 'parking meter', 'bench',
    'bird', 'cat', 'dog', 'horse', 'sheep', 'cow', 'elephant', 'bear', 'zebra',
    'giraffe', 'backpack', 'umbrella', 'handbag', 'tie', 'suitcase', 'frisbee',
    'skis', 'snowboard', 'sports ball', 'kite', 'baseball bat', 'baseball glove',
    'skateboard', 'surfboard', 'tennis racket', 'bottle', 'wine glass', 'cup',
    'fork', 'knife', 'spoon', 'bowl', 'banana', 'apple', 'sandwich', 'orange',
    'broccoli', 'carrot', 'hot dog', 'pizza', 'donut', 'cake', 'chair', 'couch',
    'potted plant', 'bed', 'dining table', 'toilet', 'tv', 'laptop', 'mouse',
    'remote', 'keyboard', 'cell phone', 'microwave', 'oven', 'toaster', 'sink',
    'refrigerator', 'book', 'clock', 'vase', 'scissors', 'teddy bear', 'hair drier',
    'toothbrush'
]

def load_default_models():
    """加载默认模型"""
    try:
        for model_name in default_models.keys():
            logger.info(f"正在加载模型: {model_name}")
            default_models[model_name] = YOLO(f"{model_name}.pt")
            logger.info(f"模型 {model_name} 加载成功")
    except Exception as e:
        logger.error(f"加载默认模型失败: {str(e)}")

def get_model(model_name):
    """获取模型实例"""
    if model_name in default_models and default_models[model_name] is not None:
        return default_models[model_name]
    elif model_name in models:
        return models[model_name]
    else:
        # 尝试加载模型
        try:
            model = YOLO(f"{model_name}.pt")
            default_models[model_name] = model
            return model
        except:
            return default_models['yolo11m']  # 返回默认模型

def decode_base64_image(image_data):
    """解码base64图像"""
    try:
        # 移除data:image前缀
        if ',' in image_data:
            image_data = image_data.split(',')[1]
        
        # 解码base64
        image_bytes = base64.b64decode(image_data)
        image = Image.open(io.BytesIO(image_bytes))
        
        # 转换为OpenCV格式
        image_cv = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
        return image_cv
    except Exception as e:
        logger.error(f"解码图像失败: {str(e)}")
        return None

def encode_image_to_base64(image):
    """将OpenCV图像编码为base64"""
    try:
        # 转换为PIL图像
        image_pil = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        
        # 编码为base64
        buffer = io.BytesIO()
        image_pil.save(buffer, format='JPEG', quality=90)
        image_base64 = base64.b64encode(buffer.getvalue()).decode()
        
        return f"data:image/jpeg;base64,{image_base64}"
    except Exception as e:
        logger.error(f"编码图像失败: {str(e)}")
        return None

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'available_models': list(default_models.keys())
    })

@app.route('/api/models', methods=['GET'])
def get_available_models():
    """获取可用模型列表"""
    available_models = []
    for model_name, model in default_models.items():
        available_models.append({
            'name': model_name,
            'loaded': model is not None,
            'description': f"YOLO11 {model_name.upper()} 模型"
        })
    
    return jsonify({'models': available_models})

@app.route('/api/detect/image', methods=['POST'])
def detect_image():
    """图像检测接口"""
    try:
        data = request.get_json()
        
        if not data or 'image' not in data:
            return jsonify({'error': '缺少图像数据'}), 400
        
        # 获取参数
        model_name = data.get('model', 'yolo11m')
        conf_threshold = float(data.get('conf', 0.25))
        iou_threshold = float(data.get('iou', 0.45))
        return_image = data.get('return_image', True)
        
        # 解码图像
        image = decode_base64_image(data['image'])
        if image is None:
            return jsonify({'error': '图像解码失败'}), 400
        
        # 获取模型
        model = get_model(model_name)
        
        # 执行检测
        results = model(image, conf=conf_threshold, iou=iou_threshold)
        
        # 处理结果
        detections = []
        annotated_image = image.copy()
        
        if results[0].boxes is not None:
            boxes = results[0].boxes
            for i, box in enumerate(boxes):
                # 提取检测信息
                x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                confidence = box.conf[0].cpu().numpy()
                class_id = int(box.cls[0].cpu().numpy())
                class_name = COCO_CLASSES[class_id] if class_id < len(COCO_CLASSES) else f"class_{class_id}"
                
                detection = {
                    'class_id': class_id,
                    'class_name': class_name,
                    'confidence': float(confidence),
                    'bbox': [float(x1), float(y1), float(x2), float(y2)]
                }
                detections.append(detection)
                
                # 在图像上绘制检测框
                if return_image:
                    cv2.rectangle(annotated_image, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)
                    label = f"{class_name}: {confidence:.2f}"
                    cv2.putText(annotated_image, label, (int(x1), int(y1-10)), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
        
        # 准备响应
        response = {
            'success': True,
            'detections': detections,
            'total_detections': len(detections),
            'model_used': model_name,
            'parameters': {
                'conf_threshold': conf_threshold,
                'iou_threshold': iou_threshold
            }
        }
        
        # 如果需要返回标注图像
        if return_image:
            annotated_base64 = encode_image_to_base64(annotated_image)
            if annotated_base64:
                response['annotated_image'] = annotated_base64
        
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"图像检测失败: {str(e)}")
        return jsonify({'error': f'检测失败: {str(e)}'}), 500

@app.route('/api/detect/video', methods=['POST'])
def detect_video():
    """视频检测接口"""
    try:
        if 'video' not in request.files:
            return jsonify({'error': '缺少视频文件'}), 400
        
        video_file = request.files['video']
        model_name = request.form.get('model', 'yolo11m')
        conf_threshold = float(request.form.get('conf', 0.25))
        iou_threshold = float(request.form.get('iou', 0.45))
        
        # 保存临时视频文件
        with tempfile.NamedTemporaryFile(delete=False, suffix='.mp4') as temp_video:
            video_file.save(temp_video.name)
            temp_video_path = temp_video.name
        
        # 获取模型
        model = get_model(model_name)
        
        # 处理视频
        cap = cv2.VideoCapture(temp_video_path)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        
        all_detections = []
        frame_detections = {}
        
        frame_idx = 0
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # 每5帧检测一次以提高性能
            if frame_idx % 5 == 0:
                results = model(frame, conf=conf_threshold, iou=iou_threshold)
                
                frame_detection = []
                if results[0].boxes is not None:
                    boxes = results[0].boxes
                    for box in boxes:
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                        confidence = box.conf[0].cpu().numpy()
                        class_id = int(box.cls[0].cpu().numpy())
                        class_name = COCO_CLASSES[class_id] if class_id < len(COCO_CLASSES) else f"class_{class_id}"
                        
                        detection = {
                            'frame': frame_idx,
                            'timestamp': frame_idx / fps,
                            'class_id': class_id,
                            'class_name': class_name,
                            'confidence': float(confidence),
                            'bbox': [float(x1), float(y1), float(x2), float(y2)]
                        }
                        frame_detection.append(detection)
                        all_detections.append(detection)
                
                frame_detections[frame_idx] = frame_detection
            
            frame_idx += 1
        
        cap.release()
        os.unlink(temp_video_path)  # 删除临时文件
        
        # 统计信息
        class_counts = {}
        for detection in all_detections:
            class_name = detection['class_name']
            class_counts[class_name] = class_counts.get(class_name, 0) + 1
        
        response = {
            'success': True,
            'total_frames': frame_count,
            'processed_frames': len(frame_detections),
            'total_detections': len(all_detections),
            'class_counts': class_counts,
            'detections': all_detections,
            'frame_detections': frame_detections,
            'video_info': {
                'fps': fps,
                'duration': frame_count / fps
            },
            'model_used': model_name
        }
        
        return jsonify(response)
        
    except Exception as e:
        logger.error(f"视频检测失败: {str(e)}")
        return jsonify({'error': f'检测失败: {str(e)}'}), 500

@app.route('/api/upload/model', methods=['POST'])
def upload_custom_model():
    """上传自定义模型"""
    try:
        if 'model' not in request.files:
            return jsonify({'error': '缺少模型文件'}), 400
        
        model_file = request.files['model']
        model_name = request.form.get('name', 'custom_model')
        
        # 保存模型文件
        model_path = f"models/{model_name}.pt"
        os.makedirs('models', exist_ok=True)
        model_file.save(model_path)
        
        # 加载模型
        try:
            custom_model = YOLO(model_path)
            models[model_name] = custom_model
            
            return jsonify({
                'success': True,
                'message': f'模型 {model_name} 上传并加载成功',
                'model_name': model_name
            })
        except Exception as e:
            os.unlink(model_path)  # 删除无效文件
            return jsonify({'error': f'模型加载失败: {str(e)}'}), 400
            
    except Exception as e:
        logger.error(f"模型上传失败: {str(e)}")
        return jsonify({'error': f'上传失败: {str(e)}'}), 500

if __name__ == '__main__':
    logger.info("正在启动YOLO11检测服务器...")
    
    # 加载默认模型
    load_default_models()
    
    # 启动服务器
    app.run(host='0.0.0.0', port=5000, debug=True)
