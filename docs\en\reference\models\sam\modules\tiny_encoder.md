---
description: Explore the detailed implementation of TinyViT architecture including Conv2d_BN, PatchEmbed, MBConv, and more in Ultralytics.
keywords: Ultralytics, TinyViT, Conv2d_BN, PatchEmbed, MBConv, Attention, PyTorch, YOLO, Deep Learning
---

# Reference for `ultralytics/models/sam/modules/tiny_encoder.py`

!!! note

    This file is available at [https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/sam/modules/tiny_encoder.py](https://github.com/ultralytics/ultralytics/blob/main/ultralytics/models/sam/modules/tiny_encoder.py). If you spot a problem please help fix it by [contributing](https://docs.ultralytics.com/help/contributing/) a [Pull Request](https://github.com/ultralytics/ultralytics/edit/main/ultralytics/models/sam/modules/tiny_encoder.py) 🛠️. Thank you 🙏!

<br>

## ::: ultralytics.models.sam.modules.tiny_encoder.Conv2d_BN

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.tiny_encoder.PatchEmbed

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.tiny_encoder.MBConv

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.tiny_encoder.PatchMerging

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.tiny_encoder.ConvLayer

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.tiny_encoder.Mlp

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.tiny_encoder.Attention

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.tiny_encoder.TinyViTBlock

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.tiny_encoder.BasicLayer

<br><br><hr><br>

## ::: ultralytics.models.sam.modules.tiny_encoder.TinyViT

<br><br>
