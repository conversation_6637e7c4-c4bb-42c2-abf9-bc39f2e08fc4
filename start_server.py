#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO11 检测系统启动脚本
自动检查依赖、下载模型并启动服务
"""

import os
import sys
import subprocess
import urllib.request
import threading
import time
import webbrowser
from pathlib import Path

def check_dependencies():
    """检查并安装依赖"""
    required_packages = [
        'ultralytics',
        'flask',
        'flask-cors',
        'opencv-python',
        'pillow',
        'numpy'
    ]
    
    print("🔍 检查依赖包...")
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")
    
    if missing_packages:
        print(f"\n📦 正在安装缺失的包: {', '.join(missing_packages)}")
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install'
            ] + missing_packages)
            print("✅ 依赖包安装完成")
        except subprocess.CalledProcessError:
            print("❌ 依赖包安装失败，请手动安装")
            return False
    
    return True

def download_models():
    """下载YOLO模型"""
    models = ['yolo11n.pt', 'yolo11s.pt', 'yolo11m.pt']
    base_url = 'https://github.com/ultralytics/assets/releases/download/v8.3.0/'
    
    print("\n🤖 检查YOLO模型...")
    
    for model in models:
        if not os.path.exists(model):
            print(f"📥 正在下载 {model}...")
            try:
                urllib.request.urlretrieve(f"{base_url}{model}", model)
                print(f"✅ {model} 下载完成")
            except Exception as e:
                print(f"❌ {model} 下载失败: {e}")
                print(f"请手动下载: {base_url}{model}")
        else:
            print(f"✅ {model} 已存在")

def start_api_server():
    """启动API服务器"""
    print("\n🚀 启动API服务器...")
    try:
        subprocess.run([sys.executable, 'yolo_api.py'])
    except KeyboardInterrupt:
        print("\n⏹️ 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")

def open_browser():
    """打开浏览器"""
    time.sleep(3)  # 等待服务器启动
    html_file = Path('yolo11_detection_system.html').absolute()
    if html_file.exists():
        print(f"🌐 正在打开浏览器: {html_file}")
        webbrowser.open(f'file://{html_file}')
    else:
        print("❌ HTML文件不存在")

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 YOLO11 智能检测系统启动器")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        print("❌ 依赖检查失败，请手动安装依赖包")
        return
    
    # 下载模型
    download_models()
    
    # 检查必要文件
    required_files = ['yolo_api.py', 'yolo11_detection_system.html']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        return
    
    print("\n✅ 所有检查通过，准备启动系统...")
    print("\n📋 使用说明:")
    print("1. API服务器将在 http://localhost:5000 启动")
    print("2. 网页界面将自动在浏览器中打开")
    print("3. 上传图片或视频文件进行检测")
    print("4. 按 Ctrl+C 停止服务器")
    print("\n" + "=" * 60)
    
    # 在后台线程中打开浏览器
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    # 启动API服务器
    start_api_server()

if __name__ == '__main__':
    main()
