# YOLO11 完整使用指南

## 项目简介

这是基于 Ultralytics YOLO11 的目标检测项目。YOLO11 是 YOLO 系列的最新版本，具有以下特点：

- **快速准确**：最先进的目标检测性能
- **易于使用**：简单的 Python API 和命令行接口
- **多任务支持**：目标检测、实例分割、图像分类、姿态估计
- **灵活部署**：支持 CPU、GPU 和各种硬件平台

## 环境安装

### 系统要求
- Python >= 3.8
- PyTorch >= 1.8

### 安装方法

#### 方法1：使用 pip 安装（推荐）
```bash
pip install ultralytics
```

#### 方法2：从源码安装
```bash
git clone https://github.com/ultralytics/ultralytics.git
cd ultralytics
pip install -e .
```

#### 方法3：使用 conda 安装
```bash
conda install -c conda-forge ultralytics
```

### 验证安装
```python
from ultralytics import YOLO
print("YOLO11 安装成功！")
```

## 快速开始

### 1. 使用预训练模型进行推理

#### Python 方式
```python
from ultralytics import YOLO

# 加载预训练模型
model = YOLO("yolo11n.pt")  # nano版本，速度最快
# model = YOLO("yolo11s.pt")  # small版本
# model = YOLO("yolo11m.pt")  # medium版本
# model = YOLO("yolo11l.pt")  # large版本
# model = YOLO("yolo11x.pt")  # extra large版本，精度最高

# 对图像进行推理
results = model("path/to/image.jpg")
results[0].show()  # 显示结果

# 对视频进行推理
results = model("path/to/video.mp4")
for result in results:
    result.show()
```

#### 命令行方式
```bash
# 图像推理
yolo predict model=yolo11n.pt source='path/to/image.jpg'

# 视频推理
yolo predict model=yolo11n.pt source='path/to/video.mp4'

# 摄像头推理
yolo predict model=yolo11n.pt source=0

# 网络图像推理
yolo predict model=yolo11n.pt source='https://ultralytics.com/images/bus.jpg'
```

## 数据集准备

### YOLO 格式数据集结构
```
datasets/
├── my_dataset/
│   ├── images/
│   │   ├── train/
│   │   │   ├── image1.jpg
│   │   │   ├── image2.jpg
│   │   │   └── ...
│   │   ├── val/
│   │   │   ├── image1.jpg
│   │   │   ├── image2.jpg
│   │   │   └── ...
│   │   └── test/ (可选)
│   │       ├── image1.jpg
│   │       └── ...
│   └── labels/
│       ├── train/
│       │   ├── image1.txt
│       │   ├── image2.txt
│       │   └── ...
│       ├── val/
│       │   ├── image1.txt
│       │   ├── image2.txt
│       │   └── ...
│       └── test/ (可选)
│           ├── image1.txt
│           └── ...
```

### 标注文件格式
每个图像对应一个同名的 `.txt` 标注文件，格式为：
```
class_id center_x center_y width height
```

其中：
- `class_id`：类别索引（从0开始）
- `center_x, center_y`：边界框中心点坐标（相对于图像宽高的比例，0-1之间）
- `width, height`：边界框宽高（相对于图像宽高的比例，0-1之间）

示例：
```
0 0.5 0.5 0.3 0.4
1 0.2 0.3 0.1 0.2
```

### 数据集配置文件
创建 `my_dataset.yaml` 文件：
```yaml
# 数据集根目录
path: ../datasets/my_dataset

# 训练、验证、测试集路径（相对于path）
train: images/train
val: images/val
test: images/test  # 可选

# 类别数量
nc: 2

# 类别名称
names:
  0: person
  1: car
```

## 自定义数据集训练

### 1. 基础训练

#### Python 方式
```python
from ultralytics import YOLO

# 加载预训练模型
model = YOLO("yolo11n.pt")

# 训练模型
results = model.train(
    data="my_dataset.yaml",  # 数据集配置文件
    epochs=100,              # 训练轮数
    imgsz=640,              # 输入图像尺寸
    batch=16,               # 批次大小
    device="0",             # GPU设备（"cpu" 或 "0,1,2,3"）
    workers=8,              # 数据加载线程数
    project="runs/train",   # 项目目录
    name="my_model",        # 实验名称
)
```

#### 命令行方式
```bash
yolo detect train data=my_dataset.yaml model=yolo11n.pt epochs=100 imgsz=640 batch=16
```

### 2. 高级训练参数

```python
model = YOLO("yolo11n.pt")

results = model.train(
    data="my_dataset.yaml",
    epochs=300,
    imgsz=640,
    batch=16,
    
    # 学习率设置
    lr0=0.01,               # 初始学习率
    lrf=0.01,               # 最终学习率
    momentum=0.937,         # SGD动量
    weight_decay=0.0005,    # 权重衰减
    
    # 数据增强
    hsv_h=0.015,           # 色调增强
    hsv_s=0.7,             # 饱和度增强
    hsv_v=0.4,             # 明度增强
    degrees=0.0,           # 旋转角度
    translate=0.1,         # 平移
    scale=0.5,             # 缩放
    shear=0.0,             # 剪切
    perspective=0.0,       # 透视变换
    flipud=0.0,            # 上下翻转概率
    fliplr=0.5,            # 左右翻转概率
    mosaic=1.0,            # 马赛克增强概率
    mixup=0.0,             # 混合增强概率
    
    # 训练设置
    patience=100,          # 早停耐心值
    save=True,             # 保存检查点
    save_period=10,        # 保存周期
    cache=False,           # 缓存图像
    device="0",            # 设备
    workers=8,             # 工作线程
    project="runs/train",  # 项目目录
    name="advanced_model", # 实验名称
    exist_ok=False,        # 覆盖现有项目
    pretrained=True,       # 使用预训练权重
    optimizer="SGD",       # 优化器 (SGD, Adam, AdamW, RMSProp)
    verbose=True,          # 详细输出
    seed=0,                # 随机种子
    deterministic=True,    # 确定性训练
    single_cls=False,      # 单类训练
    rect=False,            # 矩形训练
    cos_lr=False,          # 余弦学习率调度
    close_mosaic=10,       # 关闭马赛克增强的最后轮数
    resume=False,          # 恢复训练
    amp=True,              # 自动混合精度
    fraction=1.0,          # 数据集使用比例
    profile=False,         # 性能分析
    freeze=None,           # 冻结层数
)
```

### 3. 多GPU训练
```bash
# 使用多个GPU训练
yolo detect train data=my_dataset.yaml model=yolo11n.pt epochs=100 device=0,1,2,3
```

## 模型验证和测试

### 验证模型性能
```python
# 验证训练好的模型
model = YOLO("runs/train/my_model/weights/best.pt")
metrics = model.val(data="my_dataset.yaml")

print(f"mAP50: {metrics.box.map50}")
print(f"mAP50-95: {metrics.box.map}")
```

### 命令行验证
```bash
yolo detect val model=runs/train/my_model/weights/best.pt data=my_dataset.yaml
```

## 模型推理和部署

### 1. 批量推理
```python
model = YOLO("runs/train/my_model/weights/best.pt")

# 对文件夹中的所有图像进行推理
results = model("path/to/images/")

# 保存结果
for i, result in enumerate(results):
    result.save(f"output/result_{i}.jpg")
```

### 2. 实时推理
```python
import cv2
from ultralytics import YOLO

model = YOLO("runs/train/my_model/weights/best.pt")

# 打开摄像头
cap = cv2.VideoCapture(0)

while True:
    ret, frame = cap.read()
    if not ret:
        break
    
    # 推理
    results = model(frame)
    
    # 绘制结果
    annotated_frame = results[0].plot()
    
    # 显示
    cv2.imshow("YOLO11 Real-time Detection", annotated_frame)
    
    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

cap.release()
cv2.destroyAllWindows()
```

### 3. 模型导出
```python
model = YOLO("runs/train/my_model/weights/best.pt")

# 导出为ONNX格式
model.export(format="onnx")

# 导出为TensorRT格式
model.export(format="engine")

# 导出为CoreML格式
model.export(format="coreml")

# 导出为TensorFlow格式
model.export(format="tf")
```

## 常用配置文件

### 训练配置文件示例 (train_config.yaml)
```yaml
# 模型配置
model: yolo11n.pt
data: my_dataset.yaml

# 训练参数
epochs: 100
batch: 16
imgsz: 640
device: 0

# 优化器设置
optimizer: SGD
lr0: 0.01
lrf: 0.01
momentum: 0.937
weight_decay: 0.0005

# 数据增强
hsv_h: 0.015
hsv_s: 0.7
hsv_v: 0.4
degrees: 0.0
translate: 0.1
scale: 0.5
flipud: 0.0
fliplr: 0.5
mosaic: 1.0
mixup: 0.0

# 其他设置
patience: 100
save: true
workers: 8
project: runs/train
name: my_experiment
```

使用配置文件训练：
```bash
yolo train cfg=train_config.yaml
```

## 常见问题和解决方案

### 1. 内存不足
- 减小 `batch` 大小
- 减小 `imgsz` 图像尺寸
- 设置 `cache=False`

### 2. 训练速度慢
- 增加 `workers` 数量
- 使用更快的存储设备
- 启用 `cache=True`（如果内存充足）

### 3. 精度不高
- 增加训练轮数 `epochs`
- 使用更大的模型（如 yolo11m.pt 或 yolo11l.pt）
- 增加数据集大小
- 调整数据增强参数

### 4. 过拟合
- 增加数据增强强度
- 使用更小的模型
- 增加 `weight_decay`
- 减少训练轮数

## 项目结构说明

```
ultralytics-main/
├── ultralytics/           # 核心代码
│   ├── cfg/              # 配置文件
│   │   ├── models/       # 模型配置
│   │   └── datasets/     # 数据集配置
│   ├── data/             # 数据处理
│   ├── engine/           # 训练引擎
│   ├── models/           # 模型定义
│   ├── nn/               # 神经网络模块
│   └── utils/            # 工具函数
├── examples/             # 示例代码
├── docs/                 # 文档
├── tests/                # 测试代码
└── runs/                 # 训练输出目录
    ├── train/            # 训练结果
    ├── val/              # 验证结果
    └── predict/          # 推理结果
```

## 数据标注工具推荐

### 1. Labelme
```bash
pip install labelme
labelme
```

### 2. LabelImg
```bash
pip install labelImg
labelImg
```

### 3. CVAT (Computer Vision Annotation Tool)
- 在线标注平台
- 支持团队协作
- 网址：https://cvat.org/

### 4. Roboflow
- 在线数据管理和标注平台
- 自动数据增强
- 网址：https://roboflow.com/

## 数据格式转换

### COCO 格式转 YOLO 格式
```python
from ultralytics.data.converter import convert_coco

# 转换COCO数据集为YOLO格式
convert_coco(
    labels_dir="path/to/coco/annotations/",
    save_dir="path/to/yolo/dataset/",
    use_segments=False,  # True for segmentation
    use_keypoints=False, # True for pose estimation
    cls91to80=True       # 将91类映射到80类
)
```

### Pascal VOC 格式转 YOLO 格式
```python
import xml.etree.ElementTree as ET
import os
from PIL import Image

def convert_voc_to_yolo(voc_dir, yolo_dir, classes):
    """
    将Pascal VOC格式转换为YOLO格式

    Args:
        voc_dir: VOC数据集目录
        yolo_dir: 输出YOLO格式目录
        classes: 类别列表
    """
    os.makedirs(f"{yolo_dir}/images", exist_ok=True)
    os.makedirs(f"{yolo_dir}/labels", exist_ok=True)

    for xml_file in os.listdir(f"{voc_dir}/Annotations"):
        if not xml_file.endswith('.xml'):
            continue

        tree = ET.parse(f"{voc_dir}/Annotations/{xml_file}")
        root = tree.getroot()

        # 获取图像尺寸
        img_width = int(root.find('size/width').text)
        img_height = int(root.find('size/height').text)

        # 创建YOLO标注文件
        txt_file = xml_file.replace('.xml', '.txt')
        with open(f"{yolo_dir}/labels/{txt_file}", 'w') as f:
            for obj in root.findall('object'):
                class_name = obj.find('name').text
                if class_name not in classes:
                    continue

                class_id = classes.index(class_name)

                # 获取边界框坐标
                bbox = obj.find('bndbox')
                xmin = int(bbox.find('xmin').text)
                ymin = int(bbox.find('ymin').text)
                xmax = int(bbox.find('xmax').text)
                ymax = int(bbox.find('ymax').text)

                # 转换为YOLO格式
                center_x = (xmin + xmax) / 2.0 / img_width
                center_y = (ymin + ymax) / 2.0 / img_height
                width = (xmax - xmin) / img_width
                height = (ymax - ymin) / img_height

                f.write(f"{class_id} {center_x} {center_y} {width} {height}\n")

# 使用示例
classes = ['person', 'car', 'bicycle']  # 定义你的类别
convert_voc_to_yolo("path/to/voc", "path/to/yolo", classes)
```

## 模型微调技巧

### 1. 学习率调度
```python
# 使用余弦学习率调度
model.train(
    data="my_dataset.yaml",
    epochs=100,
    lr0=0.01,
    lrf=0.001,
    cos_lr=True,  # 启用余弦学习率调度
)
```

### 2. 渐进式训练
```python
# 第一阶段：冻结骨干网络
model = YOLO("yolo11n.pt")
model.train(
    data="my_dataset.yaml",
    epochs=50,
    freeze=10,  # 冻结前10层
    lr0=0.001,
)

# 第二阶段：解冻所有层
model.train(
    data="my_dataset.yaml",
    epochs=50,
    freeze=0,   # 不冻结任何层
    lr0=0.0001, # 使用更小的学习率
    resume=True # 从上次训练继续
)
```

### 3. 数据增强策略
```python
# 强数据增强（适用于小数据集）
model.train(
    data="my_dataset.yaml",
    epochs=100,
    hsv_h=0.1,      # 更强的色调变化
    hsv_s=0.9,      # 更强的饱和度变化
    hsv_v=0.6,      # 更强的明度变化
    degrees=15,     # 旋转角度
    translate=0.2,  # 平移范围
    scale=0.8,      # 缩放范围
    shear=10,       # 剪切角度
    perspective=0.001, # 透视变换
    flipud=0.5,     # 上下翻转
    fliplr=0.5,     # 左右翻转
    mosaic=1.0,     # 马赛克增强
    mixup=0.2,      # 混合增强
)
```

## 性能优化

### 1. 推理速度优化
```python
# 使用TensorRT加速（需要安装TensorRT）
model = YOLO("model.pt")
model.export(format="engine", half=True)  # 导出TensorRT引擎
model_trt = YOLO("model.engine")  # 加载TensorRT模型

# 使用ONNX Runtime加速
model.export(format="onnx", half=True)
model_onnx = YOLO("model.onnx")

# 使用OpenVINO加速（Intel设备）
model.export(format="openvino", half=True)
model_ov = YOLO("model_openvino_model/")
```

### 2. 内存优化
```python
# 启用梯度检查点（减少内存使用）
import torch
torch.backends.cudnn.benchmark = True

# 使用混合精度训练
model.train(
    data="my_dataset.yaml",
    epochs=100,
    amp=True,  # 自动混合精度
    batch=32,  # 可以使用更大的批次
)
```

### 3. 多尺度训练
```python
# 多尺度训练提高模型鲁棒性
model.train(
    data="my_dataset.yaml",
    epochs=100,
    imgsz=[480, 512, 544, 576, 608, 640],  # 多个尺度
)
```

## 模型集成和后处理

### 1. 测试时增强 (TTA)
```python
# 启用测试时增强
model = YOLO("model.pt")
results = model("image.jpg", augment=True)  # TTA推理
```

### 2. 非极大值抑制 (NMS) 调优
```python
# 调整NMS参数
results = model("image.jpg",
                conf=0.25,    # 置信度阈值
                iou=0.45,     # IoU阈值
                max_det=1000, # 最大检测数量
                agnostic_nms=False)  # 类别无关NMS
```

### 3. 模型集成
```python
# 多模型集成
models = [
    YOLO("model1.pt"),
    YOLO("model2.pt"),
    YOLO("model3.pt")
]

def ensemble_predict(models, image_path):
    all_results = []
    for model in models:
        results = model(image_path)
        all_results.append(results[0])

    # 这里可以实现投票机制或加权平均
    return all_results

# 使用集成模型
ensemble_results = ensemble_predict(models, "test_image.jpg")
```

## 自定义损失函数

### 1. 焦点损失 (Focal Loss)
```python
import torch
import torch.nn as nn

class FocalLoss(nn.Module):
    def __init__(self, alpha=1, gamma=2):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma

    def forward(self, pred, target):
        ce_loss = nn.CrossEntropyLoss()(pred, target)
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1-pt)**self.gamma * ce_loss
        return focal_loss

# 在训练中使用自定义损失函数需要修改源码
```

### 2. 类别平衡损失
```python
# 计算类别权重
from collections import Counter
import numpy as np

def calculate_class_weights(label_files):
    class_counts = Counter()

    for label_file in label_files:
        with open(label_file, 'r') as f:
            for line in f:
                class_id = int(line.split()[0])
                class_counts[class_id] += 1

    total_samples = sum(class_counts.values())
    num_classes = len(class_counts)

    weights = {}
    for class_id, count in class_counts.items():
        weights[class_id] = total_samples / (num_classes * count)

    return weights

# 使用类别权重
weights = calculate_class_weights(label_files)
print("类别权重:", weights)
```

## 监控和可视化

### 1. TensorBoard 集成
```python
# 训练时自动启用TensorBoard日志
model.train(
    data="my_dataset.yaml",
    epochs=100,
    project="runs/train",
    name="experiment1",
    # TensorBoard日志会自动保存到 runs/train/experiment1/
)

# 启动TensorBoard
# tensorboard --logdir runs/train
```

### 2. Weights & Biases (W&B) 集成
```python
# 安装wandb
# pip install wandb

import wandb

# 初始化W&B项目
wandb.init(project="yolo11-training", name="experiment1")

# 训练时会自动记录到W&B
model.train(
    data="my_dataset.yaml",
    epochs=100,
)
```

### 3. 训练过程可视化
```python
import matplotlib.pyplot as plt
import pandas as pd

# 读取训练日志
results_df = pd.read_csv("runs/train/experiment1/results.csv")

# 绘制损失曲线
plt.figure(figsize=(12, 8))

plt.subplot(2, 2, 1)
plt.plot(results_df['epoch'], results_df['train/box_loss'], label='Train Box Loss')
plt.plot(results_df['epoch'], results_df['val/box_loss'], label='Val Box Loss')
plt.xlabel('Epoch')
plt.ylabel('Box Loss')
plt.legend()

plt.subplot(2, 2, 2)
plt.plot(results_df['epoch'], results_df['train/cls_loss'], label='Train Cls Loss')
plt.plot(results_df['epoch'], results_df['val/cls_loss'], label='Val Cls Loss')
plt.xlabel('Epoch')
plt.ylabel('Classification Loss')
plt.legend()

plt.subplot(2, 2, 3)
plt.plot(results_df['epoch'], results_df['metrics/mAP50(B)'], label='mAP@0.5')
plt.plot(results_df['epoch'], results_df['metrics/mAP50-95(B)'], label='mAP@0.5:0.95')
plt.xlabel('Epoch')
plt.ylabel('mAP')
plt.legend()

plt.subplot(2, 2, 4)
plt.plot(results_df['epoch'], results_df['lr/pg0'], label='Learning Rate')
plt.xlabel('Epoch')
plt.ylabel('Learning Rate')
plt.legend()

plt.tight_layout()
plt.savefig('training_curves.png', dpi=300, bbox_inches='tight')
plt.show()
```

## 部署方案

### 1. Flask Web API 部署
```python
from flask import Flask, request, jsonify
from ultralytics import YOLO
import cv2
import numpy as np
import base64

app = Flask(__name__)
model = YOLO("best.pt")

@app.route('/predict', methods=['POST'])
def predict():
    try:
        # 获取base64编码的图像
        image_data = request.json['image']

        # 解码图像
        image_bytes = base64.b64decode(image_data)
        nparr = np.frombuffer(image_bytes, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

        # 推理
        results = model(image)

        # 提取结果
        detections = []
        for result in results:
            boxes = result.boxes
            if boxes is not None:
                for box in boxes:
                    detection = {
                        'class': int(box.cls),
                        'confidence': float(box.conf),
                        'bbox': box.xyxy[0].tolist()
                    }
                    detections.append(detection)

        return jsonify({'detections': detections})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
```

### 2. FastAPI 部署
```python
from fastapi import FastAPI, File, UploadFile
from ultralytics import YOLO
import cv2
import numpy as np
from PIL import Image
import io

app = FastAPI()
model = YOLO("best.pt")

@app.post("/predict/")
async def predict(file: UploadFile = File(...)):
    # 读取上传的图像
    contents = await file.read()
    image = Image.open(io.BytesIO(contents))

    # 转换为OpenCV格式
    image_cv = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)

    # 推理
    results = model(image_cv)

    # 提取结果
    detections = []
    for result in results:
        boxes = result.boxes
        if boxes is not None:
            for box in boxes:
                detection = {
                    'class': int(box.cls),
                    'confidence': float(box.conf),
                    'bbox': box.xyxy[0].tolist()
                }
                detections.append(detection)

    return {"detections": detections}

# 运行: uvicorn main:app --host 0.0.0.0 --port 8000
```

### 3. Docker 部署
```dockerfile
# Dockerfile
FROM ultralytics/ultralytics:latest

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 5000

CMD ["python", "app.py"]
```

```bash
# 构建Docker镜像
docker build -t yolo11-api .

# 运行容器
docker run -p 5000:5000 yolo11-api
```

### 4. 移动端部署 (ONNX)
```python
# 导出为ONNX格式
model = YOLO("best.pt")
model.export(format="onnx", imgsz=640, half=True)

# 移动端推理代码示例 (使用onnxruntime)
import onnxruntime as ort
import numpy as np

class YOLOMobileInference:
    def __init__(self, onnx_path):
        self.session = ort.InferenceSession(onnx_path)
        self.input_name = self.session.get_inputs()[0].name
        self.output_names = [output.name for output in self.session.get_outputs()]

    def preprocess(self, image):
        # 预处理图像
        image = cv2.resize(image, (640, 640))
        image = image.astype(np.float32) / 255.0
        image = np.transpose(image, (2, 0, 1))
        image = np.expand_dims(image, axis=0)
        return image

    def predict(self, image):
        input_data = self.preprocess(image)
        outputs = self.session.run(self.output_names, {self.input_name: input_data})
        return outputs

# 使用示例
mobile_model = YOLOMobileInference("best.onnx")
results = mobile_model.predict(image)
```

## 高级应用场景

### 1. 视频流处理
```python
import cv2
from ultralytics import YOLO
from collections import defaultdict
import numpy as np

class VideoProcessor:
    def __init__(self, model_path):
        self.model = YOLO(model_path)
        self.track_history = defaultdict(lambda: [])

    def process_video(self, video_path, output_path):
        cap = cv2.VideoCapture(video_path)

        # 获取视频属性
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

        # 创建视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))

        frame_count = 0
        while True:
            ret, frame = cap.read()
            if not ret:
                break

            # 目标检测和跟踪
            results = self.model.track(frame, persist=True)

            # 绘制结果
            if results[0].boxes is not None and results[0].boxes.id is not None:
                boxes = results[0].boxes.xywh.cpu()
                track_ids = results[0].boxes.id.int().cpu().tolist()
                confidences = results[0].boxes.conf.float().cpu().tolist()

                for box, track_id, conf in zip(boxes, track_ids, confidences):
                    x, y, w, h = box

                    # 记录轨迹
                    self.track_history[track_id].append((float(x), float(y)))
                    if len(self.track_history[track_id]) > 30:
                        self.track_history[track_id].pop(0)

                    # 绘制边界框
                    x1, y1 = int(x - w/2), int(y - h/2)
                    x2, y2 = int(x + w/2), int(y + h/2)
                    cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                    cv2.putText(frame, f'ID: {track_id} ({conf:.2f})',
                               (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)

                    # 绘制轨迹
                    points = np.array(self.track_history[track_id], dtype=np.int32)
                    if len(points) > 1:
                        cv2.polylines(frame, [points], False, (0, 0, 255), 2)

            out.write(frame)
            frame_count += 1

            if frame_count % 30 == 0:
                print(f"已处理 {frame_count} 帧")

        cap.release()
        out.release()
        print(f"视频处理完成，保存到: {output_path}")

# 使用示例
processor = VideoProcessor("best.pt")
processor.process_video("input_video.mp4", "output_video.mp4")
```

### 2. 实时人群计数
```python
import cv2
from ultralytics import YOLO
import numpy as np

class CrowdCounter:
    def __init__(self, model_path):
        self.model = YOLO(model_path)
        self.counting_line = None
        self.people_count = 0
        self.tracked_objects = {}

    def set_counting_line(self, p1, p2):
        """设置计数线"""
        self.counting_line = (p1, p2)

    def line_intersection(self, line1, line2):
        """检查两条线是否相交"""
        x1, y1, x2, y2 = line1
        x3, y3, x4, y4 = line2

        denom = (x1-x2)*(y3-y4) - (y1-y2)*(x3-x4)
        if abs(denom) < 1e-10:
            return False

        t = ((x1-x3)*(y3-y4) - (y1-y3)*(x3-x4)) / denom
        u = -((x1-x2)*(y1-y3) - (y1-y2)*(x1-x3)) / denom

        return 0 <= t <= 1 and 0 <= u <= 1

    def process_frame(self, frame):
        results = self.model.track(frame, classes=[0], persist=True)  # 只检测人

        if results[0].boxes is not None and results[0].boxes.id is not None:
            boxes = results[0].boxes.xywh.cpu()
            track_ids = results[0].boxes.id.int().cpu().tolist()

            for box, track_id in zip(boxes, track_ids):
                x, y, w, h = box
                center = (int(x), int(y))

                # 更新轨迹
                if track_id in self.tracked_objects:
                    prev_center = self.tracked_objects[track_id][-1]
                    self.tracked_objects[track_id].append(center)

                    # 检查是否穿越计数线
                    if self.counting_line and len(self.tracked_objects[track_id]) >= 2:
                        if self.line_intersection(
                            (*prev_center, *center),
                            (*self.counting_line[0], *self.counting_line[1])
                        ):
                            self.people_count += 1
                            print(f"人数统计: {self.people_count}")
                else:
                    self.tracked_objects[track_id] = [center]

                # 绘制边界框
                x1, y1 = int(x - w/2), int(y - h/2)
                x2, y2 = int(x + w/2), int(y + h/2)
                cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                cv2.circle(frame, center, 3, (0, 0, 255), -1)

        # 绘制计数线
        if self.counting_line:
            cv2.line(frame, self.counting_line[0], self.counting_line[1], (255, 0, 0), 3)

        # 显示计数
        cv2.putText(frame, f'Count: {self.people_count}', (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 255), 2)

        return frame

# 使用示例
counter = CrowdCounter("yolo11n.pt")
counter.set_counting_line((100, 300), (500, 300))  # 设置水平计数线

cap = cv2.VideoCapture(0)
while True:
    ret, frame = cap.read()
    if not ret:
        break

    processed_frame = counter.process_frame(frame)
    cv2.imshow('Crowd Counter', processed_frame)

    if cv2.waitKey(1) & 0xFF == ord('q'):
        break

cap.release()
cv2.destroyAllWindows()
```

## 故障排除和常见问题

### 1. 安装问题

#### CUDA 版本不匹配
```bash
# 检查CUDA版本
nvidia-smi

# 安装对应的PyTorch版本
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

#### 依赖包冲突
```bash
# 创建新的虚拟环境
conda create -n yolo11 python=3.9
conda activate yolo11

# 或使用venv
python -m venv yolo11_env
source yolo11_env/bin/activate  # Linux/Mac
# yolo11_env\Scripts\activate  # Windows
```

### 2. 训练问题

#### 内存不足 (CUDA out of memory)
```python
# 解决方案1: 减小批次大小
model.train(data="dataset.yaml", batch=8)  # 从16减到8

# 解决方案2: 减小图像尺寸
model.train(data="dataset.yaml", imgsz=416)  # 从640减到416

# 解决方案3: 启用梯度累积
model.train(data="dataset.yaml", batch=4, accumulate=4)  # 等效于batch=16
```

#### 训练速度慢
```python
# 解决方案1: 增加工作线程
model.train(data="dataset.yaml", workers=8)

# 解决方案2: 启用缓存
model.train(data="dataset.yaml", cache=True)

# 解决方案3: 使用更快的数据加载
model.train(data="dataset.yaml", cache="ram")  # 缓存到内存
```

#### 损失不收敛
```python
# 解决方案1: 调整学习率
model.train(data="dataset.yaml", lr0=0.001)  # 降低学习率

# 解决方案2: 增加warmup
model.train(data="dataset.yaml", warmup_epochs=5)

# 解决方案3: 检查数据质量
# 确保标注正确，图像质量良好
```

### 3. 数据集问题

#### 标注格式错误
```python
# 验证标注文件
def validate_labels(label_dir):
    import os
    for label_file in os.listdir(label_dir):
        if not label_file.endswith('.txt'):
            continue

        with open(os.path.join(label_dir, label_file), 'r') as f:
            for line_num, line in enumerate(f, 1):
                parts = line.strip().split()
                if len(parts) != 5:
                    print(f"错误: {label_file}:{line_num} - 格式不正确")
                    continue

                try:
                    class_id = int(parts[0])
                    x, y, w, h = map(float, parts[1:])

                    # 检查坐标范围
                    if not (0 <= x <= 1 and 0 <= y <= 1 and 0 <= w <= 1 and 0 <= h <= 1):
                        print(f"警告: {label_file}:{line_num} - 坐标超出范围")

                except ValueError:
                    print(f"错误: {label_file}:{line_num} - 数值格式错误")

validate_labels("path/to/labels")
```

#### 类别不平衡
```python
# 计算类别分布
def analyze_dataset(label_dir):
    from collections import Counter
    import os

    class_counts = Counter()
    total_objects = 0

    for label_file in os.listdir(label_dir):
        if not label_file.endswith('.txt'):
            continue

        with open(os.path.join(label_dir, label_file), 'r') as f:
            for line in f:
                if line.strip():
                    class_id = int(line.split()[0])
                    class_counts[class_id] += 1
                    total_objects += 1

    print("类别分布:")
    for class_id, count in sorted(class_counts.items()):
        percentage = count / total_objects * 100
        print(f"类别 {class_id}: {count} 个对象 ({percentage:.1f}%)")

    return class_counts

class_distribution = analyze_dataset("path/to/labels")
```

### 4. 推理问题

#### 检测精度低
```python
# 解决方案1: 调整置信度阈值
results = model("image.jpg", conf=0.1)  # 降低阈值

# 解决方案2: 调整NMS阈值
results = model("image.jpg", iou=0.3)  # 降低IoU阈值

# 解决方案3: 使用测试时增强
results = model("image.jpg", augment=True)
```

#### 推理速度慢
```python
# 解决方案1: 使用更小的模型
model = YOLO("yolo11n.pt")  # 使用nano版本

# 解决方案2: 降低输入分辨率
results = model("image.jpg", imgsz=416)

# 解决方案3: 导出为优化格式
model.export(format="onnx", half=True)
model_onnx = YOLO("model.onnx")
```

### 5. 部署问题

#### 模型文件过大
```python
# 解决方案1: 模型剪枝
# 需要使用专门的剪枝工具

# 解决方案2: 量化
model.export(format="onnx", int8=True)

# 解决方案3: 使用更小的模型
model = YOLO("yolo11n.pt")  # 2.6M参数
```

#### 跨平台兼容性
```python
# 使用ONNX格式确保跨平台兼容
model.export(format="onnx", opset=11)

# 或使用TensorFlow Lite用于移动端
model.export(format="tflite", int8=True)
```

## 性能基准测试

### 1. 模型性能对比
```python
import time
from ultralytics import YOLO

def benchmark_model(model_path, test_images, num_runs=100):
    model = YOLO(model_path)

    # 预热
    for _ in range(10):
        model(test_images[0])

    # 测试推理时间
    start_time = time.time()
    for _ in range(num_runs):
        for img in test_images:
            results = model(img)
    end_time = time.time()

    avg_time = (end_time - start_time) / (num_runs * len(test_images))
    fps = 1 / avg_time

    print(f"模型: {model_path}")
    print(f"平均推理时间: {avg_time*1000:.2f} ms")
    print(f"FPS: {fps:.1f}")

    return avg_time, fps

# 测试不同模型
models = ["yolo11n.pt", "yolo11s.pt", "yolo11m.pt"]
test_images = ["test1.jpg", "test2.jpg", "test3.jpg"]

for model_path in models:
    benchmark_model(model_path, test_images)
```

### 2. 精度评估
```python
def evaluate_model(model_path, test_dataset_yaml):
    model = YOLO(model_path)

    # 在测试集上评估
    metrics = model.val(data=test_dataset_yaml, split="test")

    print(f"模型: {model_path}")
    print(f"mAP@0.5: {metrics.box.map50:.3f}")
    print(f"mAP@0.5:0.95: {metrics.box.map:.3f}")
    print(f"精确率: {metrics.box.mp:.3f}")
    print(f"召回率: {metrics.box.mr:.3f}")

    return metrics

# 评估模型
metrics = evaluate_model("best.pt", "test_dataset.yaml")
```

## 最佳实践总结

### 1. 数据准备
- ✅ 确保数据质量高，标注准确
- ✅ 数据集大小适中（每类至少100-1000张图像）
- ✅ 训练/验证/测试集比例为 70%/20%/10%
- ✅ 数据增强适度，避免过度增强

### 2. 模型选择
- ✅ 小数据集使用 yolo11n 或 yolo11s
- ✅ 大数据集可使用 yolo11m 或 yolo11l
- ✅ 对精度要求极高时使用 yolo11x

### 3. 训练策略
- ✅ 从预训练模型开始
- ✅ 使用适当的学习率（0.01 for SGD, 0.001 for Adam）
- ✅ 监控验证损失，避免过拟合
- ✅ 使用早停机制

### 4. 部署优化
- ✅ 根据硬件选择合适的模型格式
- ✅ 使用批处理提高吞吐量
- ✅ 缓存常用结果
- ✅ 监控系统资源使用

## 下一步

1. **准备您的数据集**
   - 收集高质量图像
   - 使用标注工具进行精确标注
   - 验证数据集质量

2. **创建数据集配置文件**
   - 编写 YAML 配置文件
   - 设置正确的路径和类别

3. **开始训练您的第一个模型**
   - 选择合适的预训练模型
   - 设置训练参数
   - 监控训练过程

4. **验证模型性能**
   - 在验证集上评估
   - 分析混淆矩阵
   - 调整超参数

5. **部署模型进行实际应用**
   - 选择部署方案
   - 优化推理性能
   - 监控生产环境

## 参考资源

### 官方资源
- [官方文档](https://docs.ultralytics.com/)
- [GitHub仓库](https://github.com/ultralytics/ultralytics)
- [模型下载](https://github.com/ultralytics/assets/releases)

### 社区资源
- [Discord社区](https://discord.com/invite/ultralytics)
- [Reddit论坛](https://reddit.com/r/ultralytics)
- [官方论坛](https://community.ultralytics.com/)

### 学习资源
- [YouTube教程](https://youtube.com/ultralytics)
- [Colab示例](https://colab.research.google.com/github/ultralytics/ultralytics/blob/main/examples/tutorial.ipynb)
- [Kaggle竞赛](https://www.kaggle.com/models/ultralytics/yolo11)

祝您使用 YOLO11 愉快！🚀
