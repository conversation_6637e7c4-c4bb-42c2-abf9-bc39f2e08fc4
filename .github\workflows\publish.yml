# Ultralytics YOLO 🚀, AGPL-3.0 license
# Publish pip package to PyPI https://pypi.org/project/ultralytics/

name: Publish to PyPI

on:
  push:
    branches: [main]
  workflow_dispatch:
    inputs:
      pypi:
        type: boolean
        description: Publish to PyPI

jobs:
  publish:
    if: github.repository == 'ultralytics/ultralytics' && github.actor == 'glenn-jocher'
    name: Publish
    runs-on: ubuntu-latest
    permissions:
      id-token: write # for PyPI trusted publishing
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets._GITHUB_TOKEN }} # use your PAT here
      - name: Git config
        run: |
          git config --global user.name "UltralyticsAssistant"
          git config --global user.email "<EMAIL>"
      - name: Set up Python environment
        uses: actions/setup-python@v5
        with:
          python-version: "3.x"
          cache: "pip" # caching pip dependencies
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip wheel
          pip install ultralytics-actions build twine toml
      - name: Check PyPI version
        shell: python
        run: |
          import os
          from actions.utils import check_pypi_version
          local_version, online_version, publish = check_pypi_version()
          os.system(f'echo "increment={publish}" >> $GITHUB_OUTPUT')
          os.system(f'echo "current_tag=v{local_version}" >> $GITHUB_OUTPUT')
          os.system(f'echo "previous_tag=v{online_version}" >> $GITHUB_OUTPUT')
          if publish:
              print('Ready to publish new version to PyPI ✅.')
        id: check_pypi
      - name: Build package
        if: (github.event_name == 'push' || github.event.inputs.pypi == 'true') && steps.check_pypi.outputs.increment == 'True'
        run: python -m build
      - name: Publish to PyPI
        continue-on-error: true
        if: (github.event_name == 'push' || github.event.inputs.pypi == 'true') && steps.check_pypi.outputs.increment == 'True'
        uses: pypa/gh-action-pypi-publish@release/v1
      - name: Publish new tag
        if: (github.event_name == 'push' || github.event.inputs.pypi == 'true')  && steps.check_pypi.outputs.increment == 'True'
        run: |
          git tag -a "${{ steps.check_pypi.outputs.current_tag }}" -m "$(git log -1 --pretty=%B)"  # i.e. "v0.1.2 commit message"
          git push origin "${{ steps.check_pypi.outputs.current_tag }}"
      - name: Publish new release
        if: (github.event_name == 'push' || github.event.inputs.pypi == 'true')  && steps.check_pypi.outputs.increment == 'True'
        env:
          OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
          GITHUB_TOKEN: ${{ secrets._GITHUB_TOKEN }}
          CURRENT_TAG: ${{ steps.check_pypi.outputs.current_tag }}
          PREVIOUS_TAG: ${{ steps.check_pypi.outputs.previous_tag }}
        run: ultralytics-actions-summarize-release
        shell: bash
      - name: Extract PR Details
        env:
          GH_TOKEN: ${{ secrets._GITHUB_TOKEN }}
        run: |
          # Check if the event is a pull request or pull_request_target
          if [ "${{ github.event_name }}" = "pull_request" ] || [ "${{ github.event_name }}" = "pull_request_target" ]; then
            PR_NUMBER=${{ github.event.pull_request.number }}
            PR_TITLE=$(gh pr view $PR_NUMBER --json title --jq '.title')
          else
            # Use gh to find the PR associated with the commit
            COMMIT_SHA=${{ github.event.after }}
            PR_JSON=$(gh pr list --search "${COMMIT_SHA}" --state merged --json number,title --jq '.[0]')
            PR_NUMBER=$(echo $PR_JSON | jq -r '.number')
            PR_TITLE=$(echo $PR_JSON | jq -r '.title')
          fi
          echo "PR_NUMBER=$PR_NUMBER" >> $GITHUB_ENV
          echo "PR_TITLE=$PR_TITLE" >> $GITHUB_ENV
      - name: Notify on Slack (Success)
        if: success() && github.event_name == 'push' && steps.check_pypi.outputs.increment == 'True'
        uses: slackapi/slack-github-action@v1.27.0
        with:
          payload: |
            {"text": "<!channel> GitHub Actions success for ${{ github.workflow }} ✅\n\n\n*Repository:* https://github.com/${{ github.repository }}\n*Action:* https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}\n*Author:* ${{ github.actor }}\n*Event:* NEW '${{ github.repository }} ${{ steps.check_pypi.outputs.current_tag }}' pip package published 😃\n*Job Status:* ${{ job.status }}\n*Pull Request:* <https://github.com/${{ github.repository }}/pull/${{ env.PR_NUMBER }}> ${{ env.PR_TITLE }}\n"}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL_YOLO }}
      - name: Notify on Slack (Failure)
        if: failure()
        uses: slackapi/slack-github-action@v1.27.0
        with:
          payload: |
            {"text": "<!channel> GitHub Actions error for ${{ github.workflow }} ❌\n\n\n*Repository:* https://github.com/${{ github.repository }}\n*Action:* https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}\n*Author:* ${{ github.actor }}\n*Event:* ${{ github.event_name }}\n*Job Status:* ${{ job.status }}\n*Pull Request:* <https://github.com/${{ github.repository }}/pull/${{ env.PR_NUMBER }}> ${{ env.PR_TITLE }}\n"}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL_YOLO }}
