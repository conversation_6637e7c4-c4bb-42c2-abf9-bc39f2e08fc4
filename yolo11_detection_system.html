<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YOLO11 智能识别系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            color: #333;
            margin: 0;
            overflow-x: hidden;
        }

        .app-layout {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 280px;
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 0;
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
            transition: transform 0.3s ease;
        }

        .sidebar-header {
            padding: 30px 20px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            text-align: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-header h2 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .sidebar-header p {
            margin: 5px 0 0 0;
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .nav-menu {
            padding: 20px 0;
        }

        .nav-item {
            display: block;
            padding: 15px 25px;
            color: #ecf0f1;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
            cursor: pointer;
        }

        .nav-item:hover {
            background: rgba(52, 152, 219, 0.2);
            border-left-color: #3498db;
            transform: translateX(5px);
        }

        .nav-item.active {
            background: rgba(52, 152, 219, 0.3);
            border-left-color: #3498db;
        }

        .nav-item i {
            margin-right: 12px;
            width: 20px;
            text-align: center;
        }

        .main-content {
            margin-left: 280px;
            flex: 1;
            padding: 20px;
            transition: margin-left 0.3s ease;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .page-header {
            background: white;
            padding: 25px 30px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
            border-left: 5px solid #3498db;
        }

        .page-header h1 {
            margin: 0;
            font-size: 2.2rem;
            color: #2c3e50;
            font-weight: 600;
        }

        .page-header p {
            margin: 8px 0 0 0;
            color: #7f8c8d;
            font-size: 1.1rem;
        }

        .content-grid {
            display: grid;
            grid-template-columns: 1fr 1.5fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .page-content {
            display: none;
        }

        .page-content.active {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .control-panel {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            height: fit-content;
            border: 1px solid #e8ecef;
        }

        .control-section {
            margin-bottom: 30px;
        }

        .control-section h3 {
            color: #2980b9;
            margin-bottom: 15px;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .upload-area {
            border: 2px dashed #3498db;
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .upload-area::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(52, 152, 219, 0.1), transparent);
            transition: left 0.6s ease;
        }

        .upload-area:hover {
            border-color: #2980b9;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            transform: translateY(-3px);
            box-shadow: 0 12px 40px rgba(52, 152, 219, 0.2);
        }

        .upload-area:hover::before {
            left: 100%;
        }

        .upload-area.dragover {
            border-color: #27ae60;
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 3rem;
            color: #3498db;
            margin-bottom: 15px;
        }

        .file-input {
            display: none;
        }

        .model-selector {
            margin-bottom: 20px;
        }

        .model-selector select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 1rem;
            background: white;
            transition: all 0.3s ease;
        }

        .model-selector select:focus {
            border-color: #3498db;
            outline: none;
            box-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
        }

        .btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 15px;
            position: relative;
            overflow: hidden;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(52, 152, 219, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .display-area {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid #e8ecef;
        }

        .preview-container {
            position: relative;
            margin-bottom: 20px;
            border-radius: 12px;
            overflow: hidden;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            min-height: 400px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            border: 2px dashed #dee2e6;
            transition: all 0.3s ease;
        }

        .preview-container:hover {
            border-color: #3498db;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        }

        .preview-image, .preview-video {
            max-width: 100%;
            max-height: 500px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .loading-spinner {
            display: none;
            width: 50px;
            height: 50px;
            border: 5px solid #e0e0e0;
            border-top: 5px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .results-panel {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            margin-top: 30px;
            border: 1px solid #e8ecef;
            animation: slideUp 0.5s ease;
        }

        @keyframes slideUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 25px rgba(52, 152, 219, 0.3);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
        }

        .detection-list {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            max-height: 300px;
            overflow-y: auto;
        }

        .detection-item {
            background: white;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 10px;
            border-left: 4px solid #3498db;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease;
        }

        .detection-item:hover {
            transform: translateX(5px);
        }

        .confidence-bar {
            background: #e0e0e0;
            height: 8px;
            border-radius: 4px;
            margin-top: 10px;
            overflow: hidden;
        }

        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #27ae60, #2ecc71);
            border-radius: 4px;
            transition: width 0.5s ease;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e0e0e0;
            border-radius: 3px;
            overflow: hidden;
            margin-top: 15px;
            display: none;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2980b9);
            border-radius: 3px;
            width: 0%;
            transition: width 0.3s ease;
        }

        /* 新功能样式 */
        .annotation-tools {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .tool-btn {
            flex: 1;
            padding: 10px;
            border: 2px solid #e0e0e0;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .tool-btn.active {
            border-color: #3498db;
            background: #3498db;
            color: white;
        }

        .annotation-canvas-container {
            position: relative;
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .annotation-info {
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #2c3e50;
        }

        .form-group select,
        .form-group input[type="range"] {
            width: 100%;
            padding: 10px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
        }

        .training-monitor {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .training-charts {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .training-log {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }

        .export-formats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .export-formats label {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .export-formats label:hover {
            background: #e9ecef;
        }

        .export-results {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .sidebar-toggle {
            display: none;
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: #3498db;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .sidebar-toggle {
                display: block;
            }

            .content-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .training-charts {
                grid-template-columns: 1fr;
            }

            .export-formats {
                grid-template-columns: 1fr;
            }
        }

        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="floating-particles" id="particles"></div>

    <div class="app-layout">
        <!-- 左侧导航栏 -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>🚀 YOLO11</h2>
                <p>智能检测系统</p>
            </div>
            <nav class="nav-menu">
                <a href="#" class="nav-item active" data-page="detection">
                    <i>🔍</i> 目标检测
                </a>
                <a href="#" class="nav-item" data-page="annotation">
                    <i>🏷️</i> 图像标注
                </a>
                <a href="#" class="nav-item" data-page="training">
                    <i>🎯</i> 模型训练
                </a>
                <a href="#" class="nav-item" data-page="export">
                    <i>📦</i> 模型导出
                </a>
                <a href="#" class="nav-item" data-page="analytics">
                    <i>📊</i> 数据分析
                </a>
                <a href="#" class="nav-item" data-page="settings">
                    <i>⚙️</i> 系统设置
                </a>
            </nav>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <div class="container">
                <!-- 目标检测页面 -->
                <div class="page-content active" id="detection-page">
                    <div class="page-header">
                        <h1>🔍 目标检测</h1>
                        <p>上传图片或视频，使用YOLO11进行智能目标检测</p>
                        <div class="progress-bar" id="progressBar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                    </div>

                    <div class="content-grid">
                        <div class="control-panel">
                            <div class="control-section">
                                <h3>📁 文件上传</h3>
                                <div class="upload-area" id="uploadArea">
                                    <div class="upload-icon">📤</div>
                                    <p>拖拽文件到此处或点击选择</p>
                                    <p style="font-size: 0.9rem; color: #666; margin-top: 10px;">
                                        支持格式: JPG, PNG, GIF, MP4, AVI, MOV
                                    </p>
                                    <input type="file" id="fileInput" class="file-input"
                                           accept="image/*,video/*" multiple>
                                </div>
                            </div>

                            <div class="control-section">
                                <h3>🤖 模型选择</h3>
                                <div class="model-selector">
                                    <select id="modelSelect">
                                        <option value="yolo11n">YOLO11n (快速模式)</option>
                                        <option value="yolo11s">YOLO11s (平衡模式)</option>
                                        <option value="yolo11m" selected>YOLO11m (精确模式)</option>
                                        <option value="custom">自定义模型</option>
                                    </select>
                                </div>
                                <input type="file" id="customModelInput" accept=".pt,.onnx"
                                       style="display: none; margin-top: 10px;">
                            </div>

                            <div class="control-section">
                                <h3>⚙️ 检测参数</h3>
                                <div style="margin-bottom: 15px;">
                                    <label>置信度阈值: <span id="confValue">0.25</span></label>
                                    <input type="range" id="confSlider" min="0.1" max="0.9" step="0.05" value="0.25"
                                           style="width: 100%; margin-top: 5px;">
                                </div>
                                <div style="margin-bottom: 15px;">
                                    <label>IoU阈值: <span id="iouValue">0.45</span></label>
                                    <input type="range" id="iouSlider" min="0.1" max="0.9" step="0.05" value="0.45"
                                           style="width: 100%; margin-top: 5px;">
                                </div>
                            </div>

                            <button class="btn" id="detectBtn" disabled>🔍 开始检测</button>
                            <button class="btn" id="clearBtn">🗑️ 清除结果</button>
                        </div>

                        <div class="display-area">
                            <h3 style="color: #2c3e50; margin-bottom: 20px;">📊 检测结果展示</h3>
                            <div class="preview-container" id="previewContainer">
                                <div style="text-align: center; color: #666;">
                                    <div style="font-size: 4rem; margin-bottom: 20px;">🖼️</div>
                                    <p>请上传图片或视频文件开始检测</p>
                                </div>
                                <div class="loading-spinner" id="loadingSpinner"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 检测结果面板 -->
                <div class="results-panel" id="resultsPanel" style="display: none;">
                    <h3 style="color: #2c3e50; margin-bottom: 20px;">📈 检测统计</h3>
                    <div class="stats-grid" id="statsGrid">
                        <!-- 统计卡片将通过JavaScript动态生成 -->
                    </div>

                    <h3 style="color: #2c3e50; margin-bottom: 20px;">📋 检测详情</h3>
                    <div class="detection-list" id="detectionList">
                        <!-- 检测结果列表将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 图像标注页面 -->
                <div class="page-content" id="annotation-page">
                    <div class="page-header">
                        <h1>🏷️ 图像标注</h1>
                        <p>创建和编辑训练数据集的标注信息</p>
                    </div>

                    <div class="content-grid">
                        <div class="control-panel">
                            <div class="control-section">
                                <h3>📂 数据集管理</h3>
                                <button class="btn" id="createDatasetBtn">📁 创建新数据集</button>
                                <button class="btn" id="loadDatasetBtn">📂 加载数据集</button>
                                <input type="file" id="datasetInput" webkitdirectory style="display: none;">
                            </div>

                            <div class="control-section">
                                <h3>🏷️ 标注工具</h3>
                                <div class="annotation-tools">
                                    <button class="tool-btn active" data-tool="bbox">📦 边界框</button>
                                    <button class="tool-btn" data-tool="polygon">🔷 多边形</button>
                                    <button class="tool-btn" data-tool="point">📍 关键点</button>
                                </div>
                            </div>

                            <div class="control-section">
                                <h3>📝 类别管理</h3>
                                <div id="classManager">
                                    <input type="text" id="newClassName" placeholder="输入新类别名称">
                                    <button class="btn" id="addClassBtn">➕ 添加类别</button>
                                    <div id="classList"></div>
                                </div>
                            </div>
                        </div>

                        <div class="annotation-canvas-container">
                            <canvas id="annotationCanvas" style="border: 1px solid #ddd; border-radius: 8px;"></canvas>
                            <div class="annotation-info" id="annotationInfo">
                                <p>请加载图像开始标注</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 模型训练页面 -->
                <div class="page-content" id="training-page">
                    <div class="page-header">
                        <h1>🎯 模型训练</h1>
                        <p>使用自定义数据集训练YOLO11模型</p>
                    </div>

                    <div class="content-grid">
                        <div class="control-panel">
                            <div class="control-section">
                                <h3>📊 训练配置</h3>
                                <div class="form-group">
                                    <label>基础模型:</label>
                                    <select id="baseModelSelect">
                                        <option value="yolo11n">YOLO11n</option>
                                        <option value="yolo11s">YOLO11s</option>
                                        <option value="yolo11m" selected>YOLO11m</option>
                                        <option value="yolo11l">YOLO11l</option>
                                        <option value="yolo11x">YOLO11x</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label>训练轮数: <span id="epochsValue">100</span></label>
                                    <input type="range" id="epochsSlider" min="10" max="500" value="100">
                                </div>

                                <div class="form-group">
                                    <label>批次大小: <span id="batchValue">16</span></label>
                                    <input type="range" id="batchSlider" min="1" max="64" value="16">
                                </div>

                                <div class="form-group">
                                    <label>学习率: <span id="lrValue">0.01</span></label>
                                    <input type="range" id="lrSlider" min="0.001" max="0.1" step="0.001" value="0.01">
                                </div>
                            </div>

                            <button class="btn" id="startTrainingBtn">🚀 开始训练</button>
                            <button class="btn" id="stopTrainingBtn" disabled>⏹️ 停止训练</button>
                        </div>

                        <div class="training-monitor">
                            <h3>📈 训练监控</h3>
                            <div class="training-charts">
                                <canvas id="lossChart" width="400" height="200"></canvas>
                                <canvas id="accuracyChart" width="400" height="200"></canvas>
                            </div>
                            <div class="training-log" id="trainingLog">
                                <p>等待开始训练...</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 模型导出页面 -->
                <div class="page-content" id="export-page">
                    <div class="page-header">
                        <h1>📦 模型导出</h1>
                        <p>将训练好的模型导出为不同格式</p>
                    </div>

                    <div class="content-grid">
                        <div class="control-panel">
                            <div class="control-section">
                                <h3>🎯 选择模型</h3>
                                <select id="exportModelSelect">
                                    <option value="">选择要导出的模型</option>
                                </select>
                                <button class="btn" id="loadModelBtn">📂 加载模型文件</button>
                                <input type="file" id="modelFileInput" accept=".pt" style="display: none;">
                            </div>

                            <div class="control-section">
                                <h3>📋 导出格式</h3>
                                <div class="export-formats">
                                    <label><input type="checkbox" value="onnx" checked> ONNX (.onnx)</label>
                                    <label><input type="checkbox" value="torchscript"> TorchScript (.torchscript)</label>
                                    <label><input type="checkbox" value="tflite"> TensorFlow Lite (.tflite)</label>
                                    <label><input type="checkbox" value="coreml"> CoreML (.mlmodel)</label>
                                    <label><input type="checkbox" value="engine"> TensorRT (.engine)</label>
                                    <label><input type="checkbox" value="openvino"> OpenVINO (.xml)</label>
                                </div>
                            </div>

                            <div class="control-section">
                                <h3>⚙️ 导出选项</h3>
                                <label><input type="checkbox" id="halfPrecision"> 半精度 (FP16)</label>
                                <label><input type="checkbox" id="int8Quantization"> INT8量化</label>
                                <label><input type="checkbox" id="dynamicBatch"> 动态批次</label>
                            </div>

                            <button class="btn" id="exportBtn">📦 开始导出</button>
                        </div>

                        <div class="export-results">
                            <h3>📊 导出结果</h3>
                            <div id="exportProgress" style="display: none;">
                                <div class="progress-bar">
                                    <div class="progress-fill" id="exportProgressFill"></div>
                                </div>
                                <p id="exportStatus">准备导出...</p>
                            </div>
                            <div id="exportList"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentFile = null;
        let detectionResults = [];
        
        // DOM元素
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const modelSelect = document.getElementById('modelSelect');
        const customModelInput = document.getElementById('customModelInput');
        const confSlider = document.getElementById('confSlider');
        const iouSlider = document.getElementById('iouSlider');
        const confValue = document.getElementById('confValue');
        const iouValue = document.getElementById('iouValue');
        const detectBtn = document.getElementById('detectBtn');
        const clearBtn = document.getElementById('clearBtn');
        const previewContainer = document.getElementById('previewContainer');
        const loadingSpinner = document.getElementById('loadingSpinner');
        const resultsPanel = document.getElementById('resultsPanel');
        const statsGrid = document.getElementById('statsGrid');
        const detectionList = document.getElementById('detectionList');
        const progressBar = document.getElementById('progressBar');
        const progressFill = document.getElementById('progressFill');

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeParticles();
            setupEventListeners();
        });

        // 创建浮动粒子效果
        function initializeParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 20;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.width = Math.random() * 10 + 5 + 'px';
                particle.style.height = particle.style.width;
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // 设置事件监听器
        function setupEventListeners() {
            // 导航菜单事件
            document.querySelectorAll('.nav-item').forEach(item => {
                item.addEventListener('click', handleNavigation);
            });

            // 文件上传区域事件
            uploadArea.addEventListener('click', () => fileInput.click());
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleDrop);

            // 文件输入事件
            fileInput.addEventListener('change', handleFileSelect);

            // 模型选择事件
            modelSelect.addEventListener('change', handleModelChange);
            customModelInput.addEventListener('change', handleCustomModelSelect);

            // 滑块事件
            confSlider.addEventListener('input', updateConfValue);
            iouSlider.addEventListener('input', updateIouValue);

            // 按钮事件
            detectBtn.addEventListener('click', startDetection);
            clearBtn.addEventListener('click', clearResults);

            // 新功能事件监听器
            setupAnnotationListeners();
            setupTrainingListeners();
            setupExportListeners();
        }

        // 导航处理
        function handleNavigation(e) {
            e.preventDefault();
            const targetPage = e.target.dataset.page;

            // 更新导航状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            e.target.classList.add('active');

            // 显示对应页面
            document.querySelectorAll('.page-content').forEach(page => {
                page.classList.remove('active');
            });
            document.getElementById(`${targetPage}-page`).classList.add('active');
        }

        // 标注功能事件监听器
        function setupAnnotationListeners() {
            const createDatasetBtn = document.getElementById('createDatasetBtn');
            const loadDatasetBtn = document.getElementById('loadDatasetBtn');
            const datasetInput = document.getElementById('datasetInput');
            const addClassBtn = document.getElementById('addClassBtn');
            const newClassName = document.getElementById('newClassName');

            if (createDatasetBtn) {
                createDatasetBtn.addEventListener('click', createNewDataset);
            }
            if (loadDatasetBtn) {
                loadDatasetBtn.addEventListener('click', () => datasetInput.click());
            }
            if (datasetInput) {
                datasetInput.addEventListener('change', loadDataset);
            }
            if (addClassBtn) {
                addClassBtn.addEventListener('click', addClass);
            }
            if (newClassName) {
                newClassName.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') addClass();
                });
            }

            // 标注工具选择
            document.querySelectorAll('.tool-btn').forEach(btn => {
                btn.addEventListener('click', selectAnnotationTool);
            });
        }

        // 训练功能事件监听器
        function setupTrainingListeners() {
            const startTrainingBtn = document.getElementById('startTrainingBtn');
            const stopTrainingBtn = document.getElementById('stopTrainingBtn');
            const epochsSlider = document.getElementById('epochsSlider');
            const batchSlider = document.getElementById('batchSlider');
            const lrSlider = document.getElementById('lrSlider');

            if (startTrainingBtn) {
                startTrainingBtn.addEventListener('click', startTraining);
            }
            if (stopTrainingBtn) {
                stopTrainingBtn.addEventListener('click', stopTraining);
            }
            if (epochsSlider) {
                epochsSlider.addEventListener('input', updateEpochsValue);
            }
            if (batchSlider) {
                batchSlider.addEventListener('input', updateBatchValue);
            }
            if (lrSlider) {
                lrSlider.addEventListener('input', updateLrValue);
            }
        }

        // 导出功能事件监听器
        function setupExportListeners() {
            const loadModelBtn = document.getElementById('loadModelBtn');
            const modelFileInput = document.getElementById('modelFileInput');
            const exportBtn = document.getElementById('exportBtn');

            if (loadModelBtn) {
                loadModelBtn.addEventListener('click', () => modelFileInput.click());
            }
            if (modelFileInput) {
                modelFileInput.addEventListener('change', loadModelForExport);
            }
            if (exportBtn) {
                exportBtn.addEventListener('click', exportModel);
            }
        }

        // 拖拽事件处理
        function handleDragOver(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        }

        // 文件选择处理
        function handleFileSelect(e) {
            const files = e.target.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        }

        // 处理文件
        function handleFile(file) {
            currentFile = file;
            const fileType = file.type;

            if (fileType.startsWith('image/')) {
                displayImage(file);
            } else if (fileType.startsWith('video/')) {
                displayVideo(file);
            } else {
                showError('不支持的文件格式');
                return;
            }

            detectBtn.disabled = false;
            clearResults();
        }

        // 显示图片
        function displayImage(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewContainer.innerHTML = `
                    <img src="${e.target.result}" class="preview-image" alt="预览图片">
                `;
            };
            reader.readAsDataURL(file);
        }

        // 显示视频
        function displayVideo(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewContainer.innerHTML = `
                    <video src="${e.target.result}" class="preview-video" controls>
                        您的浏览器不支持视频播放
                    </video>
                `;
            };
            reader.readAsDataURL(file);
        }

        // 模型选择处理
        function handleModelChange() {
            if (modelSelect.value === 'custom') {
                customModelInput.style.display = 'block';
            } else {
                customModelInput.style.display = 'none';
            }
        }

        // 自定义模型选择
        function handleCustomModelSelect(e) {
            const file = e.target.files[0];
            if (file) {
                showSuccess(`已选择自定义模型: ${file.name}`);
            }
        }

        // 更新置信度值
        function updateConfValue() {
            confValue.textContent = confSlider.value;
        }

        // 更新IoU值
        function updateIouValue() {
            iouValue.textContent = iouSlider.value;
        }

        // 开始检测
        async function startDetection() {
            if (!currentFile) {
                showError('请先上传文件');
                return;
            }

            showLoading(true);
            detectBtn.disabled = true;

            try {
                // 模拟API调用
                await simulateDetection();
                showLoading(false);
                detectBtn.disabled = false;
            } catch (error) {
                showError('检测失败: ' + error.message);
                showLoading(false);
                detectBtn.disabled = false;
            }
        }

        // 实际检测过程
        async function simulateDetection() {
            // 显示进度条
            progressBar.style.display = 'block';
            progressFill.style.width = '10%';

            try {
                if (currentFile.type.startsWith('image/')) {
                    await detectImage();
                } else if (currentFile.type.startsWith('video/')) {
                    await detectVideo();
                }
            } catch (error) {
                throw error;
            }

            // 隐藏进度条
            setTimeout(() => {
                progressBar.style.display = 'none';
                progressFill.style.width = '0%';
            }, 500);
        }

        // 图像检测
        async function detectImage() {
            try {
                // 将图像转换为base64
                const base64Image = await fileToBase64(currentFile);
                progressFill.style.width = '30%';

                // 准备请求数据
                const requestData = {
                    image: base64Image,
                    model: modelSelect.value,
                    conf: parseFloat(confSlider.value),
                    iou: parseFloat(iouSlider.value),
                    return_image: true
                };

                progressFill.style.width = '50%';

                // 发送检测请求
                const response = await fetch('http://localhost:5000/api/detect/image', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                progressFill.style.width = '80%';

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                progressFill.style.width = '100%';

                if (result.success) {
                    // 转换检测结果格式
                    detectionResults = result.detections.map(det => ({
                        class: det.class_name,
                        confidence: det.confidence.toFixed(3),
                        bbox: det.bbox
                    }));

                    // 如果有标注图像，显示它，否则在原图上绘制
                    if (result.annotated_image) {
                        previewContainer.innerHTML = `
                            <img src="${result.annotated_image}" class="preview-image" alt="检测结果">
                        `;
                    } else {
                        // 在原图上绘制检测结果
                        drawDetectionResults(base64Image, result.detections);
                    }

                    // 显示结果
                    displayResults();
                } else {
                    throw new Error(result.error || '检测失败');
                }

            } catch (error) {
                // 如果API不可用，使用模拟数据
                console.warn('API不可用，使用模拟数据:', error);
                showError('API服务不可用，使用模拟数据进行演示');

                // 模拟检测进度
                for (let i = 30; i <= 100; i += 10) {
                    progressFill.style.width = i + '%';
                    await new Promise(resolve => setTimeout(resolve, 100));
                }

                // 生成模拟检测结果
                detectionResults = generateMockResults();
                displayResults();
            }
        }

        // 视频检测
        async function detectVideo() {
            try {
                progressFill.style.width = '20%';

                // 准备FormData
                const formData = new FormData();
                formData.append('video', currentFile);
                formData.append('model', modelSelect.value);
                formData.append('conf', confSlider.value);
                formData.append('iou', iouSlider.value);

                progressFill.style.width = '40%';

                // 发送检测请求
                const response = await fetch('http://localhost:5000/api/detect/video', {
                    method: 'POST',
                    body: formData
                });

                progressFill.style.width = '80%';

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                progressFill.style.width = '100%';

                if (result.success) {
                    // 转换检测结果格式
                    detectionResults = result.detections.map(det => ({
                        class: det.class_name,
                        confidence: det.confidence.toFixed(3),
                        bbox: det.bbox,
                        frame: det.frame,
                        timestamp: det.timestamp.toFixed(2) + 's'
                    }));

                    // 显示结果
                    displayResults();

                    // 显示视频统计信息
                    showVideoStats(result);
                } else {
                    throw new Error(result.error || '视频检测失败');
                }

            } catch (error) {
                // 如果API不可用，使用模拟数据
                console.warn('API不可用，使用模拟数据:', error);
                showError('API服务不可用，使用模拟数据进行演示');

                // 模拟检测进度
                for (let i = 40; i <= 100; i += 10) {
                    progressFill.style.width = i + '%';
                    await new Promise(resolve => setTimeout(resolve, 200));
                }

                // 生成模拟检测结果
                detectionResults = generateMockResults();
                displayResults();
            }
        }

        // 文件转base64
        function fileToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => resolve(reader.result);
                reader.onerror = reject;
                reader.readAsDataURL(file);
            });
        }

        // 在原图上绘制检测结果
        function drawDetectionResults(imageBase64, detections) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            img.onload = function() {
                canvas.width = img.width;
                canvas.height = img.height;

                // 绘制原图
                ctx.drawImage(img, 0, 0);

                // 找到最高置信度的检测结果
                let maxConfidence = 0;
                let maxConfidenceDetection = null;

                detections.forEach(detection => {
                    if (detection.confidence > maxConfidence) {
                        maxConfidence = detection.confidence;
                        maxConfidenceDetection = detection;
                    }
                });

                // 绘制所有检测框
                detections.forEach((detection, index) => {
                    const [x1, y1, x2, y2] = detection.bbox;
                    const isMaxConfidence = detection === maxConfidenceDetection;

                    // 设置绘制样式
                    ctx.strokeStyle = isMaxConfidence ? '#ff0000' : '#00ff00'; // 最高置信度用红色
                    ctx.lineWidth = isMaxConfidence ? 4 : 2;
                    ctx.fillStyle = isMaxConfidence ? 'rgba(255, 0, 0, 0.3)' : 'rgba(0, 255, 0, 0.3)';

                    // 绘制边界框
                    ctx.strokeRect(x1, y1, x2 - x1, y2 - y1);
                    ctx.fillRect(x1, y1, x2 - x1, y2 - y1);

                    // 绘制标签背景
                    const label = `${detection.class_name}: ${(detection.confidence * 100).toFixed(1)}%`;
                    const labelWidth = ctx.measureText(label).width + 20;
                    const labelHeight = 25;

                    ctx.fillStyle = isMaxConfidence ? '#ff0000' : '#00ff00';
                    ctx.fillRect(x1, y1 - labelHeight, labelWidth, labelHeight);

                    // 绘制标签文字
                    ctx.fillStyle = 'white';
                    ctx.font = isMaxConfidence ? 'bold 14px Arial' : '12px Arial';
                    ctx.fillText(label, x1 + 10, y1 - 8);

                    // 如果是最高置信度，添加特殊标记
                    if (isMaxConfidence) {
                        ctx.fillStyle = '#ffff00';
                        ctx.font = 'bold 16px Arial';
                        ctx.fillText('★ 最高置信度', x1, y2 + 20);
                    }
                });

                // 显示绘制后的图像
                const annotatedImageUrl = canvas.toDataURL('image/jpeg', 0.9);
                previewContainer.innerHTML = `
                    <img src="${annotatedImageUrl}" class="preview-image" alt="检测结果" style="max-width: 100%; height: auto;">
                `;

                // 添加检测信息显示
                const infoDiv = document.createElement('div');
                infoDiv.style.cssText = `
                    margin-top: 15px;
                    padding: 15px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border-radius: 10px;
                    text-align: center;
                `;
                infoDiv.innerHTML = `
                    <h4 style="margin: 0 0 10px 0;">🎯 检测摘要</h4>
                    <p style="margin: 5px 0;">总检测数: <strong>${detections.length}</strong></p>
                    <p style="margin: 5px 0;">最高置信度: <strong>${(maxConfidence * 100).toFixed(1)}%</strong></p>
                    <p style="margin: 5px 0;">最佳检测: <strong>${maxConfidenceDetection?.class_name || 'N/A'}</strong></p>
                `;
                previewContainer.appendChild(infoDiv);
            };

            img.src = imageBase64;
        }

        // 生成模拟检测结果
        function generateMockResults() {
            const classes = ['person', 'car', 'bicycle', 'dog', 'cat', 'bird', 'traffic light', 'stop sign'];
            const results = [];
            const numDetections = Math.floor(Math.random() * 8) + 3;

            for (let i = 0; i < numDetections; i++) {
                results.push({
                    class: classes[Math.floor(Math.random() * classes.length)],
                    confidence: (Math.random() * 0.4 + 0.6).toFixed(3),
                    bbox: [
                        Math.floor(Math.random() * 200),
                        Math.floor(Math.random() * 200),
                        Math.floor(Math.random() * 200) + 100,
                        Math.floor(Math.random() * 200) + 100
                    ]
                });
            }

            return results;
        }

        // 显示检测结果
        function displayResults() {
            // 统计数据
            const classCount = {};
            let totalDetections = detectionResults.length;
            let avgConfidence = 0;

            detectionResults.forEach(result => {
                classCount[result.class] = (classCount[result.class] || 0) + 1;
                avgConfidence += parseFloat(result.confidence);
            });

            avgConfidence = totalDetections > 0 ? (avgConfidence / totalDetections).toFixed(3) : 0;

            // 显示统计卡片
            displayStats(totalDetections, Object.keys(classCount).length, avgConfidence);

            // 显示检测列表
            displayDetectionList();

            // 显示结果面板
            resultsPanel.style.display = 'block';
            resultsPanel.scrollIntoView({ behavior: 'smooth' });
        }

        // 显示统计数据
        function displayStats(total, classes, avgConf) {
            statsGrid.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${total}</div>
                    <div class="stat-label">检测目标</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${classes}</div>
                    <div class="stat-label">目标类别</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${avgConf}</div>
                    <div class="stat-label">平均置信度</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${modelSelect.value.toUpperCase()}</div>
                    <div class="stat-label">使用模型</div>
                </div>
            `;
        }

        // 显示检测列表
        function displayDetectionList() {
            detectionList.innerHTML = '';

            detectionResults.forEach((result, index) => {
                const item = document.createElement('div');
                item.className = 'detection-item';

                // 检查是否是视频检测结果
                const isVideo = result.frame !== undefined;
                const timeInfo = isVideo ? `帧: ${result.frame}, 时间: ${result.timestamp}` : `位置: [${result.bbox.map(x => Math.round(x)).join(', ')}]`;

                item.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <strong>${result.class}</strong>
                            <span style="color: #666; margin-left: 10px;">
                                ${timeInfo}
                            </span>
                        </div>
                        <div style="text-align: right;">
                            <div style="font-weight: bold; color: #27ae60;">
                                ${(result.confidence * 100).toFixed(1)}%
                            </div>
                        </div>
                    </div>
                    <div class="confidence-bar">
                        <div class="confidence-fill" style="width: ${result.confidence * 100}%"></div>
                    </div>
                `;
                detectionList.appendChild(item);
            });
        }

        // 显示视频统计信息
        function showVideoStats(videoResult) {
            const videoInfo = videoResult.video_info;
            const classCount = videoResult.class_counts;

            // 添加视频信息到统计面板
            const videoStatsCard = document.createElement('div');
            videoStatsCard.className = 'stat-card';
            videoStatsCard.style.gridColumn = 'span 2';
            videoStatsCard.innerHTML = `
                <div class="stat-number">${videoInfo.duration.toFixed(1)}s</div>
                <div class="stat-label">视频时长 (${videoInfo.fps} FPS)</div>
            `;
            statsGrid.appendChild(videoStatsCard);

            // 显示类别统计
            if (Object.keys(classCount).length > 0) {
                const classStatsContainer = document.createElement('div');
                classStatsContainer.style.marginTop = '20px';
                classStatsContainer.innerHTML = '<h4 style="color: #2980b9; margin-bottom: 15px;">类别统计</h4>';

                const classGrid = document.createElement('div');
                classGrid.style.display = 'grid';
                classGrid.style.gridTemplateColumns = 'repeat(auto-fit, minmax(150px, 1fr))';
                classGrid.style.gap = '10px';

                Object.entries(classCount).forEach(([className, count]) => {
                    const classCard = document.createElement('div');
                    classCard.style.cssText = `
                        background: linear-gradient(45deg, #e74c3c, #c0392b);
                        color: white;
                        padding: 15px;
                        border-radius: 10px;
                        text-align: center;
                        box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
                    `;
                    classCard.innerHTML = `
                        <div style="font-size: 1.5rem; font-weight: bold;">${count}</div>
                        <div style="font-size: 0.9rem; opacity: 0.9;">${className}</div>
                    `;
                    classGrid.appendChild(classCard);
                });

                classStatsContainer.appendChild(classGrid);
                resultsPanel.appendChild(classStatsContainer);
            }
        }

        // 清除结果
        function clearResults() {
            resultsPanel.style.display = 'none';
            detectionResults = [];
            statsGrid.innerHTML = '';
            detectionList.innerHTML = '';
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                loadingSpinner.style.display = 'block';
                previewContainer.style.opacity = '0.5';
            } else {
                loadingSpinner.style.display = 'none';
                previewContainer.style.opacity = '1';
            }
        }

        // 显示成功消息
        function showSuccess(message) {
            showNotification(message, 'success');
        }

        // 显示错误消息
        function showError(message) {
            showNotification(message, 'error');
        }

        // 显示通知
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 25px;
                border-radius: 10px;
                color: white;
                font-weight: bold;
                z-index: 1000;
                animation: slideIn 0.3s ease;
                max-width: 300px;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            `;

            if (type === 'success') {
                notification.style.background = 'linear-gradient(45deg, #27ae60, #2ecc71)';
            } else {
                notification.style.background = 'linear-gradient(45deg, #e74c3c, #c0392b)';
            }

            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }

            .upload-area::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                transition: left 0.5s;
            }

            .upload-area:hover::before {
                left: 100%;
            }

            .btn::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                transition: left 0.5s;
            }

            .btn:hover::before {
                left: 100%;
            }

            .stat-card {
                position: relative;
                overflow: hidden;
            }

            .stat-card::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
                transition: left 0.5s;
            }

            .stat-card:hover::before {
                left: 100%;
            }
        `;
        document.head.appendChild(style);

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'o':
                        e.preventDefault();
                        fileInput.click();
                        break;
                    case 'Enter':
                        e.preventDefault();
                        if (!detectBtn.disabled) {
                            startDetection();
                        }
                        break;
                    case 'Delete':
                        e.preventDefault();
                        clearResults();
                        break;
                }
            }
        });

        // 标注功能实现
        function createNewDataset() {
            showSuccess('创建新数据集功能开发中...');
        }

        function loadDataset(e) {
            const files = e.target.files;
            if (files.length > 0) {
                showSuccess(`已加载 ${files.length} 个文件`);
            }
        }

        function addClass() {
            const className = document.getElementById('newClassName').value.trim();
            if (className) {
                const classList = document.getElementById('classList');
                const classItem = document.createElement('div');
                classItem.style.cssText = `
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 8px 12px;
                    margin: 5px 0;
                    background: #f8f9fa;
                    border-radius: 6px;
                    border-left: 3px solid #3498db;
                `;
                classItem.innerHTML = `
                    <span>${className}</span>
                    <button onclick="this.parentElement.remove()" style="background: #e74c3c; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer;">删除</button>
                `;
                classList.appendChild(classItem);
                document.getElementById('newClassName').value = '';
                showSuccess(`已添加类别: ${className}`);
            }
        }

        function selectAnnotationTool(e) {
            document.querySelectorAll('.tool-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            e.target.classList.add('active');
            const tool = e.target.dataset.tool;
            showSuccess(`已选择工具: ${tool}`);
        }

        // 训练功能实现
        function updateEpochsValue() {
            document.getElementById('epochsValue').textContent = document.getElementById('epochsSlider').value;
        }

        function updateBatchValue() {
            document.getElementById('batchValue').textContent = document.getElementById('batchSlider').value;
        }

        function updateLrValue() {
            document.getElementById('lrValue').textContent = document.getElementById('lrSlider').value;
        }

        function startTraining() {
            const startBtn = document.getElementById('startTrainingBtn');
            const stopBtn = document.getElementById('stopTrainingBtn');
            const trainingLog = document.getElementById('trainingLog');

            startBtn.disabled = true;
            stopBtn.disabled = false;

            trainingLog.innerHTML = '<p>开始训练...</p>';

            // 模拟训练过程
            let epoch = 0;
            const maxEpochs = parseInt(document.getElementById('epochsSlider').value);

            const trainingInterval = setInterval(() => {
                epoch++;
                const loss = (Math.random() * 0.5 + 0.1).toFixed(4);
                const accuracy = (Math.random() * 0.3 + 0.7).toFixed(4);

                trainingLog.innerHTML += `<p>Epoch ${epoch}/${maxEpochs} - Loss: ${loss}, Accuracy: ${accuracy}</p>`;
                trainingLog.scrollTop = trainingLog.scrollHeight;

                if (epoch >= maxEpochs) {
                    clearInterval(trainingInterval);
                    startBtn.disabled = false;
                    stopBtn.disabled = true;
                    trainingLog.innerHTML += '<p style="color: #27ae60;">训练完成!</p>';
                    showSuccess('模型训练完成!');
                }
            }, 1000);

            // 保存训练间隔ID以便停止
            window.currentTrainingInterval = trainingInterval;
        }

        function stopTraining() {
            if (window.currentTrainingInterval) {
                clearInterval(window.currentTrainingInterval);
                document.getElementById('startTrainingBtn').disabled = false;
                document.getElementById('stopTrainingBtn').disabled = true;
                document.getElementById('trainingLog').innerHTML += '<p style="color: #e74c3c;">训练已停止</p>';
                showError('训练已手动停止');
            }
        }

        // 导出功能实现
        function loadModelForExport(e) {
            const file = e.target.files[0];
            if (file) {
                const select = document.getElementById('exportModelSelect');
                const option = document.createElement('option');
                option.value = file.name;
                option.textContent = file.name;
                option.selected = true;
                select.appendChild(option);
                showSuccess(`已加载模型: ${file.name}`);
            }
        }

        function exportModel() {
            const modelSelect = document.getElementById('exportModelSelect');
            const selectedModel = modelSelect.value;

            if (!selectedModel) {
                showError('请先选择要导出的模型');
                return;
            }

            const formats = [];
            document.querySelectorAll('.export-formats input[type="checkbox"]:checked').forEach(cb => {
                formats.push(cb.value);
            });

            if (formats.length === 0) {
                showError('请至少选择一种导出格式');
                return;
            }

            const exportProgress = document.getElementById('exportProgress');
            const exportProgressFill = document.getElementById('exportProgressFill');
            const exportStatus = document.getElementById('exportStatus');
            const exportList = document.getElementById('exportList');

            exportProgress.style.display = 'block';
            exportList.innerHTML = '';

            // 模拟导出过程
            let currentFormat = 0;
            const exportInterval = setInterval(() => {
                if (currentFormat < formats.length) {
                    const format = formats[currentFormat];
                    const progress = ((currentFormat + 1) / formats.length) * 100;

                    exportProgressFill.style.width = progress + '%';
                    exportStatus.textContent = `正在导出 ${format.toUpperCase()} 格式...`;

                    // 添加到导出列表
                    const exportItem = document.createElement('div');
                    exportItem.style.cssText = `
                        padding: 15px;
                        margin: 10px 0;
                        background: #f8f9fa;
                        border-radius: 8px;
                        border-left: 4px solid #27ae60;
                    `;
                    exportItem.innerHTML = `
                        <strong>${format.toUpperCase()}</strong> -
                        <span style="color: #27ae60;">导出成功</span>
                        <button style="float: right; background: #3498db; color: white; border: none; padding: 5px 10px; border-radius: 4px; cursor: pointer;">下载</button>
                    `;
                    exportList.appendChild(exportItem);

                    currentFormat++;
                } else {
                    clearInterval(exportInterval);
                    exportStatus.textContent = '所有格式导出完成!';
                    showSuccess('模型导出完成!');
                    setTimeout(() => {
                        exportProgress.style.display = 'none';
                    }, 2000);
                }
            }, 1500);
        }

        // 响应式处理
        window.addEventListener('resize', function() {
            // 重新计算粒子位置
            const particles = document.querySelectorAll('.particle');
            particles.forEach(particle => {
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
            });
        });

        // 侧边栏切换（移动端）
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('open');
        }

        // 添加移动端菜单按钮
        document.addEventListener('DOMContentLoaded', function() {
            const toggleBtn = document.createElement('button');
            toggleBtn.className = 'sidebar-toggle';
            toggleBtn.innerHTML = '☰';
            toggleBtn.onclick = toggleSidebar;
            document.body.appendChild(toggleBtn);
        });
    </script>
</body>
</html>
