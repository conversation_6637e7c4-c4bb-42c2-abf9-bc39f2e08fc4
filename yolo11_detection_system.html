<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YOLO11 智能识别系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 3rem;
            background: linear-gradient(45deg, #3498db, #2980b9);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .header p {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 20px;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .control-panel {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            height: fit-content;
        }

        .control-section {
            margin-bottom: 30px;
        }

        .control-section h3 {
            color: #2980b9;
            margin-bottom: 15px;
            font-size: 1.3rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .upload-area {
            border: 3px dashed #3498db;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            background: linear-gradient(45deg, rgba(52, 152, 219, 0.1), rgba(41, 128, 185, 0.1));
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .upload-area:hover {
            border-color: #2980b9;
            background: linear-gradient(45deg, rgba(52, 152, 219, 0.2), rgba(41, 128, 185, 0.2));
            transform: translateY(-2px);
        }

        .upload-area.dragover {
            border-color: #27ae60;
            background: linear-gradient(45deg, rgba(39, 174, 96, 0.2), rgba(46, 204, 113, 0.2));
        }

        .upload-icon {
            font-size: 3rem;
            color: #3498db;
            margin-bottom: 15px;
        }

        .file-input {
            display: none;
        }

        .model-selector {
            margin-bottom: 20px;
        }

        .model-selector select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 1rem;
            background: white;
            transition: all 0.3s ease;
        }

        .model-selector select:focus {
            border-color: #3498db;
            outline: none;
            box-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
        }

        .btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 15px;
            position: relative;
            overflow: hidden;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(52, 152, 219, 0.4);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .display-area {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .preview-container {
            position: relative;
            margin-bottom: 20px;
            border-radius: 15px;
            overflow: hidden;
            background: #f8f9fa;
            min-height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .preview-image, .preview-video {
            max-width: 100%;
            max-height: 500px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }

        .loading-spinner {
            display: none;
            width: 50px;
            height: 50px;
            border: 5px solid #e0e0e0;
            border-top: 5px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .results-panel {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            margin-top: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 25px rgba(52, 152, 219, 0.3);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
        }

        .detection-list {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            max-height: 300px;
            overflow-y: auto;
        }

        .detection-item {
            background: white;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 10px;
            border-left: 4px solid #3498db;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease;
        }

        .detection-item:hover {
            transform: translateX(5px);
        }

        .confidence-bar {
            background: #e0e0e0;
            height: 8px;
            border-radius: 4px;
            margin-top: 10px;
            overflow: hidden;
        }

        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #27ae60, #2ecc71);
            border-radius: 4px;
            transition: width 0.5s ease;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e0e0e0;
            border-radius: 3px;
            overflow: hidden;
            margin-top: 15px;
            display: none;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2980b9);
            border-radius: 3px;
            width: 0%;
            transition: width 0.3s ease;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        .floating-particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="floating-particles" id="particles"></div>
    
    <div class="container">
        <div class="header">
            <h1>🚀 YOLO11 智能识别系统</h1>
            <p>基于最新YOLO11算法的高精度目标检测平台</p>
            <div class="progress-bar" id="progressBar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="main-content">
            <div class="control-panel">
                <div class="control-section">
                    <h3>📁 文件上传</h3>
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-icon">📤</div>
                        <p>拖拽文件到此处或点击选择</p>
                        <p style="font-size: 0.9rem; color: #666; margin-top: 10px;">
                            支持格式: JPG, PNG, GIF, MP4, AVI, MOV
                        </p>
                        <input type="file" id="fileInput" class="file-input" 
                               accept="image/*,video/*" multiple>
                    </div>
                </div>

                <div class="control-section">
                    <h3>🤖 模型选择</h3>
                    <div class="model-selector">
                        <select id="modelSelect">
                            <option value="yolo11n">YOLO11n (快速模式)</option>
                            <option value="yolo11s">YOLO11s (平衡模式)</option>
                            <option value="yolo11m" selected>YOLO11m (精确模式)</option>
                            <option value="custom">自定义模型</option>
                        </select>
                    </div>
                    <input type="file" id="customModelInput" accept=".pt,.onnx" 
                           style="display: none; margin-top: 10px;">
                </div>

                <div class="control-section">
                    <h3>⚙️ 检测参数</h3>
                    <div style="margin-bottom: 15px;">
                        <label>置信度阈值: <span id="confValue">0.25</span></label>
                        <input type="range" id="confSlider" min="0.1" max="0.9" step="0.05" value="0.25" 
                               style="width: 100%; margin-top: 5px;">
                    </div>
                    <div style="margin-bottom: 15px;">
                        <label>IoU阈值: <span id="iouValue">0.45</span></label>
                        <input type="range" id="iouSlider" min="0.1" max="0.9" step="0.05" value="0.45" 
                               style="width: 100%; margin-top: 5px;">
                    </div>
                </div>

                <button class="btn" id="detectBtn" disabled>🔍 开始检测</button>
                <button class="btn" id="clearBtn">🗑️ 清除结果</button>
            </div>

            <div class="display-area">
                <h3 style="color: #2980b9; margin-bottom: 20px;">📊 检测结果展示</h3>
                <div class="preview-container" id="previewContainer">
                    <div style="text-align: center; color: #666;">
                        <div style="font-size: 4rem; margin-bottom: 20px;">🖼️</div>
                        <p>请上传图片或视频文件开始检测</p>
                    </div>
                    <div class="loading-spinner" id="loadingSpinner"></div>
                </div>
            </div>
        </div>

        <div class="results-panel" id="resultsPanel" style="display: none;">
            <h3 style="color: #2980b9; margin-bottom: 20px;">📈 检测统计</h3>
            <div class="stats-grid" id="statsGrid">
                <!-- 统计卡片将通过JavaScript动态生成 -->
            </div>
            
            <h3 style="color: #2980b9; margin-bottom: 20px;">📋 检测详情</h3>
            <div class="detection-list" id="detectionList">
                <!-- 检测结果列表将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentFile = null;
        let detectionResults = [];
        
        // DOM元素
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const modelSelect = document.getElementById('modelSelect');
        const customModelInput = document.getElementById('customModelInput');
        const confSlider = document.getElementById('confSlider');
        const iouSlider = document.getElementById('iouSlider');
        const confValue = document.getElementById('confValue');
        const iouValue = document.getElementById('iouValue');
        const detectBtn = document.getElementById('detectBtn');
        const clearBtn = document.getElementById('clearBtn');
        const previewContainer = document.getElementById('previewContainer');
        const loadingSpinner = document.getElementById('loadingSpinner');
        const resultsPanel = document.getElementById('resultsPanel');
        const statsGrid = document.getElementById('statsGrid');
        const detectionList = document.getElementById('detectionList');
        const progressBar = document.getElementById('progressBar');
        const progressFill = document.getElementById('progressFill');

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeParticles();
            setupEventListeners();
        });

        // 创建浮动粒子效果
        function initializeParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 20;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.width = Math.random() * 10 + 5 + 'px';
                particle.style.height = particle.style.width;
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // 设置事件监听器
        function setupEventListeners() {
            // 文件上传区域事件
            uploadArea.addEventListener('click', () => fileInput.click());
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleDrop);

            // 文件输入事件
            fileInput.addEventListener('change', handleFileSelect);

            // 模型选择事件
            modelSelect.addEventListener('change', handleModelChange);
            customModelInput.addEventListener('change', handleCustomModelSelect);

            // 滑块事件
            confSlider.addEventListener('input', updateConfValue);
            iouSlider.addEventListener('input', updateIouValue);

            // 按钮事件
            detectBtn.addEventListener('click', startDetection);
            clearBtn.addEventListener('click', clearResults);
        }

        // 拖拽事件处理
        function handleDragOver(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        }

        // 文件选择处理
        function handleFileSelect(e) {
            const files = e.target.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        }

        // 处理文件
        function handleFile(file) {
            currentFile = file;
            const fileType = file.type;

            if (fileType.startsWith('image/')) {
                displayImage(file);
            } else if (fileType.startsWith('video/')) {
                displayVideo(file);
            } else {
                showError('不支持的文件格式');
                return;
            }

            detectBtn.disabled = false;
            clearResults();
        }

        // 显示图片
        function displayImage(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewContainer.innerHTML = `
                    <img src="${e.target.result}" class="preview-image" alt="预览图片">
                `;
            };
            reader.readAsDataURL(file);
        }

        // 显示视频
        function displayVideo(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                previewContainer.innerHTML = `
                    <video src="${e.target.result}" class="preview-video" controls>
                        您的浏览器不支持视频播放
                    </video>
                `;
            };
            reader.readAsDataURL(file);
        }

        // 模型选择处理
        function handleModelChange() {
            if (modelSelect.value === 'custom') {
                customModelInput.style.display = 'block';
            } else {
                customModelInput.style.display = 'none';
            }
        }

        // 自定义模型选择
        function handleCustomModelSelect(e) {
            const file = e.target.files[0];
            if (file) {
                showSuccess(`已选择自定义模型: ${file.name}`);
            }
        }

        // 更新置信度值
        function updateConfValue() {
            confValue.textContent = confSlider.value;
        }

        // 更新IoU值
        function updateIouValue() {
            iouValue.textContent = iouSlider.value;
        }

        // 开始检测
        async function startDetection() {
            if (!currentFile) {
                showError('请先上传文件');
                return;
            }

            showLoading(true);
            detectBtn.disabled = true;

            try {
                // 模拟API调用
                await simulateDetection();
                showLoading(false);
                detectBtn.disabled = false;
            } catch (error) {
                showError('检测失败: ' + error.message);
                showLoading(false);
                detectBtn.disabled = false;
            }
        }

        // 实际检测过程
        async function simulateDetection() {
            // 显示进度条
            progressBar.style.display = 'block';
            progressFill.style.width = '10%';

            try {
                if (currentFile.type.startsWith('image/')) {
                    await detectImage();
                } else if (currentFile.type.startsWith('video/')) {
                    await detectVideo();
                }
            } catch (error) {
                throw error;
            }

            // 隐藏进度条
            setTimeout(() => {
                progressBar.style.display = 'none';
                progressFill.style.width = '0%';
            }, 500);
        }

        // 图像检测
        async function detectImage() {
            try {
                // 将图像转换为base64
                const base64Image = await fileToBase64(currentFile);
                progressFill.style.width = '30%';

                // 准备请求数据
                const requestData = {
                    image: base64Image,
                    model: modelSelect.value,
                    conf: parseFloat(confSlider.value),
                    iou: parseFloat(iouSlider.value),
                    return_image: true
                };

                progressFill.style.width = '50%';

                // 发送检测请求
                const response = await fetch('http://localhost:5000/api/detect/image', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestData)
                });

                progressFill.style.width = '80%';

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                progressFill.style.width = '100%';

                if (result.success) {
                    // 转换检测结果格式
                    detectionResults = result.detections.map(det => ({
                        class: det.class_name,
                        confidence: det.confidence.toFixed(3),
                        bbox: det.bbox
                    }));

                    // 如果有标注图像，显示它
                    if (result.annotated_image) {
                        previewContainer.innerHTML = `
                            <img src="${result.annotated_image}" class="preview-image" alt="检测结果">
                        `;
                    }

                    // 显示结果
                    displayResults();
                } else {
                    throw new Error(result.error || '检测失败');
                }

            } catch (error) {
                // 如果API不可用，使用模拟数据
                console.warn('API不可用，使用模拟数据:', error);
                showError('API服务不可用，使用模拟数据进行演示');

                // 模拟检测进度
                for (let i = 30; i <= 100; i += 10) {
                    progressFill.style.width = i + '%';
                    await new Promise(resolve => setTimeout(resolve, 100));
                }

                // 生成模拟检测结果
                detectionResults = generateMockResults();
                displayResults();
            }
        }

        // 视频检测
        async function detectVideo() {
            try {
                progressFill.style.width = '20%';

                // 准备FormData
                const formData = new FormData();
                formData.append('video', currentFile);
                formData.append('model', modelSelect.value);
                formData.append('conf', confSlider.value);
                formData.append('iou', iouSlider.value);

                progressFill.style.width = '40%';

                // 发送检测请求
                const response = await fetch('http://localhost:5000/api/detect/video', {
                    method: 'POST',
                    body: formData
                });

                progressFill.style.width = '80%';

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                progressFill.style.width = '100%';

                if (result.success) {
                    // 转换检测结果格式
                    detectionResults = result.detections.map(det => ({
                        class: det.class_name,
                        confidence: det.confidence.toFixed(3),
                        bbox: det.bbox,
                        frame: det.frame,
                        timestamp: det.timestamp.toFixed(2) + 's'
                    }));

                    // 显示结果
                    displayResults();

                    // 显示视频统计信息
                    showVideoStats(result);
                } else {
                    throw new Error(result.error || '视频检测失败');
                }

            } catch (error) {
                // 如果API不可用，使用模拟数据
                console.warn('API不可用，使用模拟数据:', error);
                showError('API服务不可用，使用模拟数据进行演示');

                // 模拟检测进度
                for (let i = 40; i <= 100; i += 10) {
                    progressFill.style.width = i + '%';
                    await new Promise(resolve => setTimeout(resolve, 200));
                }

                // 生成模拟检测结果
                detectionResults = generateMockResults();
                displayResults();
            }
        }

        // 文件转base64
        function fileToBase64(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => resolve(reader.result);
                reader.onerror = reject;
                reader.readAsDataURL(file);
            });
        }

        // 生成模拟检测结果
        function generateMockResults() {
            const classes = ['person', 'car', 'bicycle', 'dog', 'cat', 'bird', 'traffic light', 'stop sign'];
            const results = [];
            const numDetections = Math.floor(Math.random() * 8) + 3;

            for (let i = 0; i < numDetections; i++) {
                results.push({
                    class: classes[Math.floor(Math.random() * classes.length)],
                    confidence: (Math.random() * 0.4 + 0.6).toFixed(3),
                    bbox: [
                        Math.floor(Math.random() * 200),
                        Math.floor(Math.random() * 200),
                        Math.floor(Math.random() * 200) + 100,
                        Math.floor(Math.random() * 200) + 100
                    ]
                });
            }

            return results;
        }

        // 显示检测结果
        function displayResults() {
            // 统计数据
            const classCount = {};
            let totalDetections = detectionResults.length;
            let avgConfidence = 0;

            detectionResults.forEach(result => {
                classCount[result.class] = (classCount[result.class] || 0) + 1;
                avgConfidence += parseFloat(result.confidence);
            });

            avgConfidence = totalDetections > 0 ? (avgConfidence / totalDetections).toFixed(3) : 0;

            // 显示统计卡片
            displayStats(totalDetections, Object.keys(classCount).length, avgConfidence);

            // 显示检测列表
            displayDetectionList();

            // 显示结果面板
            resultsPanel.style.display = 'block';
            resultsPanel.scrollIntoView({ behavior: 'smooth' });
        }

        // 显示统计数据
        function displayStats(total, classes, avgConf) {
            statsGrid.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${total}</div>
                    <div class="stat-label">检测目标</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${classes}</div>
                    <div class="stat-label">目标类别</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${avgConf}</div>
                    <div class="stat-label">平均置信度</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${modelSelect.value.toUpperCase()}</div>
                    <div class="stat-label">使用模型</div>
                </div>
            `;
        }

        // 显示检测列表
        function displayDetectionList() {
            detectionList.innerHTML = '';

            detectionResults.forEach((result, index) => {
                const item = document.createElement('div');
                item.className = 'detection-item';

                // 检查是否是视频检测结果
                const isVideo = result.frame !== undefined;
                const timeInfo = isVideo ? `帧: ${result.frame}, 时间: ${result.timestamp}` : `位置: [${result.bbox.map(x => Math.round(x)).join(', ')}]`;

                item.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <strong>${result.class}</strong>
                            <span style="color: #666; margin-left: 10px;">
                                ${timeInfo}
                            </span>
                        </div>
                        <div style="text-align: right;">
                            <div style="font-weight: bold; color: #27ae60;">
                                ${(result.confidence * 100).toFixed(1)}%
                            </div>
                        </div>
                    </div>
                    <div class="confidence-bar">
                        <div class="confidence-fill" style="width: ${result.confidence * 100}%"></div>
                    </div>
                `;
                detectionList.appendChild(item);
            });
        }

        // 显示视频统计信息
        function showVideoStats(videoResult) {
            const videoInfo = videoResult.video_info;
            const classCount = videoResult.class_counts;

            // 添加视频信息到统计面板
            const videoStatsCard = document.createElement('div');
            videoStatsCard.className = 'stat-card';
            videoStatsCard.style.gridColumn = 'span 2';
            videoStatsCard.innerHTML = `
                <div class="stat-number">${videoInfo.duration.toFixed(1)}s</div>
                <div class="stat-label">视频时长 (${videoInfo.fps} FPS)</div>
            `;
            statsGrid.appendChild(videoStatsCard);

            // 显示类别统计
            if (Object.keys(classCount).length > 0) {
                const classStatsContainer = document.createElement('div');
                classStatsContainer.style.marginTop = '20px';
                classStatsContainer.innerHTML = '<h4 style="color: #2980b9; margin-bottom: 15px;">类别统计</h4>';

                const classGrid = document.createElement('div');
                classGrid.style.display = 'grid';
                classGrid.style.gridTemplateColumns = 'repeat(auto-fit, minmax(150px, 1fr))';
                classGrid.style.gap = '10px';

                Object.entries(classCount).forEach(([className, count]) => {
                    const classCard = document.createElement('div');
                    classCard.style.cssText = `
                        background: linear-gradient(45deg, #e74c3c, #c0392b);
                        color: white;
                        padding: 15px;
                        border-radius: 10px;
                        text-align: center;
                        box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
                    `;
                    classCard.innerHTML = `
                        <div style="font-size: 1.5rem; font-weight: bold;">${count}</div>
                        <div style="font-size: 0.9rem; opacity: 0.9;">${className}</div>
                    `;
                    classGrid.appendChild(classCard);
                });

                classStatsContainer.appendChild(classGrid);
                resultsPanel.appendChild(classStatsContainer);
            }
        }

        // 清除结果
        function clearResults() {
            resultsPanel.style.display = 'none';
            detectionResults = [];
            statsGrid.innerHTML = '';
            detectionList.innerHTML = '';
        }

        // 显示加载状态
        function showLoading(show) {
            if (show) {
                loadingSpinner.style.display = 'block';
                previewContainer.style.opacity = '0.5';
            } else {
                loadingSpinner.style.display = 'none';
                previewContainer.style.opacity = '1';
            }
        }

        // 显示成功消息
        function showSuccess(message) {
            showNotification(message, 'success');
        }

        // 显示错误消息
        function showError(message) {
            showNotification(message, 'error');
        }

        // 显示通知
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 25px;
                border-radius: 10px;
                color: white;
                font-weight: bold;
                z-index: 1000;
                animation: slideIn 0.3s ease;
                max-width: 300px;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            `;

            if (type === 'success') {
                notification.style.background = 'linear-gradient(45deg, #27ae60, #2ecc71)';
            } else {
                notification.style.background = 'linear-gradient(45deg, #e74c3c, #c0392b)';
            }

            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOut 0.3s ease';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }, 3000);
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }

            .upload-area::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                transition: left 0.5s;
            }

            .upload-area:hover::before {
                left: 100%;
            }

            .btn::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
                transition: left 0.5s;
            }

            .btn:hover::before {
                left: 100%;
            }

            .stat-card {
                position: relative;
                overflow: hidden;
            }

            .stat-card::before {
                content: '';
                position: absolute;
                top: 0;
                left: -100%;
                width: 100%;
                height: 100%;
                background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
                transition: left 0.5s;
            }

            .stat-card:hover::before {
                left: 100%;
            }
        `;
        document.head.appendChild(style);

        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case 'o':
                        e.preventDefault();
                        fileInput.click();
                        break;
                    case 'Enter':
                        e.preventDefault();
                        if (!detectBtn.disabled) {
                            startDetection();
                        }
                        break;
                    case 'Delete':
                        e.preventDefault();
                        clearResults();
                        break;
                }
            }
        });

        // 响应式处理
        window.addEventListener('resize', function() {
            // 重新计算粒子位置
            const particles = document.querySelectorAll('.particle');
            particles.forEach(particle => {
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
            });
        });
    </script>
</body>
</html>
